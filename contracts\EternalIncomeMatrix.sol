// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

/**
 * @title EternalIncomeMatrix
 * @dev Implementation of the Eternal Income Matrix System with 4x2 recursive spillover
 */
contract EternalIncomeMatrix is Ownable, ReentrancyGuard {
    // USDT token interface
    IERC20 public usdtToken;
    
    // Join fee in USDT (6 USD)
    uint256 public constant JOIN_FEE = 6 * 10**6; // Assuming 6 decimals for USDT on BSC
    
    // Fee distribution
    uint256 public constant DIRECT_REFERRAL_FEE = 2 * 10**6;
    uint256 public constant SPILLOVER_FEE = 2 * 10**6;
    uint256 public constant WEEKLY_FUND_FEE = 2 * 10**6;
    
    // Weekly distribution time
    uint256 public nextDistributionTime;
    uint256 public constant DISTRIBUTION_INTERVAL = 7 days;
    
    // Level thresholds
    uint256[] public levelThresholds = [4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048];
    
    // Level percentages for weekly distribution
    uint256[] public levelPercentages = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100];
    
    // User structure
    struct User {
        address userAddress;
        address referrer;
        uint256 directReferrals;
        uint256 totalTeamSize;
        uint256 spilloversReceived;
        uint256 spilloversGenerated;
        uint256 totalEarnings;
        uint256 weeklyEarnings;
        uint256 lastActiveTime;
        bool isActive;
        address[] directTeam;
        address[] spilloverTeam;
    }
    
    // Matrix structure
    struct Matrix {
        address[] directReferrals;
        address[] spilloverReferrals;
    }
    
    // Mapping of user address to User struct
    mapping(address => User) public users;
    
    // Mapping of user address to Matrix struct
    mapping(address => Matrix) public matrices;
    
    // Weekly fund
    uint256 public weeklyFund;
    
    // Total members
    uint256 public totalMembers;
    
    // Active members this week
    mapping(uint256 => address[]) public activeMembers;
    uint256 public currentWeek;
    
    // Events
    event UserJoined(address indexed user, address indexed referrer, uint256 joinFee);
    event DirectReferralPaid(address indexed referrer, address indexed user, uint256 amount);
    event SpilloverPaid(address indexed receiver, address indexed user, uint256 amount);
    event WeeklyFundDistributed(uint256 indexed week, uint256 totalAmount, uint256 membersCount);
    event UserLevelChanged(address indexed user, uint256 oldLevel, uint256 newLevel);
    
    /**
     * @dev Constructor
     * @param _usdtToken Address of the USDT token contract
     */
    constructor(address _usdtToken) {
        usdtToken = IERC20(_usdtToken);
        nextDistributionTime = block.timestamp + DISTRIBUTION_INTERVAL;
        currentWeek = 1;
    }
    
    /**
     * @dev Join the Eternal Income Matrix System
     * @param _referrer Address of the referrer
     */
    function join(address _referrer) external nonReentrant {
        require(_referrer != msg.sender, "Cannot refer yourself");
        require(users[msg.sender].userAddress == address(0), "Already joined");
        require(_referrer == address(0) || users[_referrer].userAddress != address(0), "Referrer not found");
        
        // Transfer USDT from user to contract
        require(usdtToken.transferFrom(msg.sender, address(this), JOIN_FEE), "USDT transfer failed");
        
        // Create new user
        users[msg.sender].userAddress = msg.sender;
        users[msg.sender].referrer = _referrer;
        users[msg.sender].isActive = true;
        users[msg.sender].lastActiveTime = block.timestamp;
        
        // Add to total members
        totalMembers++;
        
        // Add to active members for this week
        activeMembers[currentWeek].push(msg.sender);
        
        // Process referral if exists
        if (_referrer != address(0)) {
            // Pay direct referral fee
            require(usdtToken.transfer(_referrer, DIRECT_REFERRAL_FEE), "Direct referral payment failed");
            
            // Update referrer stats
            users[_referrer].directReferrals++;
            users[_referrer].totalTeamSize++;
            users[_referrer].directTeam.push(msg.sender);
            users[_referrer].totalEarnings += DIRECT_REFERRAL_FEE;
            
            // Add to matrix
            matrices[_referrer].directReferrals.push(msg.sender);
            
            // Check if spillover should happen
            processSpillover(_referrer, msg.sender);
            
            // Check if referrer level changed
            updateUserLevel(_referrer);
            
            emit DirectReferralPaid(_referrer, msg.sender, DIRECT_REFERRAL_FEE);
        } else {
            // If no referrer, add to weekly fund
            weeklyFund += DIRECT_REFERRAL_FEE;
        }
        
        // Add to weekly fund
        weeklyFund += WEEKLY_FUND_FEE;
        
        emit UserJoined(msg.sender, _referrer, JOIN_FEE);
    }
    
    /**
     * @dev Process spillover logic
     * @param _referrer Address of the referrer
     * @param _user Address of the new user
     */
    function processSpillover(address _referrer, address _user) internal {
        // Check if referrer has more than 4 direct referrals
        if (matrices[_referrer].directReferrals.length > 4) {
            // Every 4 referrals, 2 stay with referrer and 2 spill over
            uint256 referralCount = matrices[_referrer].directReferrals.length;
            
            if (referralCount % 4 > 2) {
                // This referral should spill over
                address upline = users[_referrer].referrer;
                
                // If upline exists
                if (upline != address(0)) {
                    // Pay spillover fee
                    require(usdtToken.transfer(upline, SPILLOVER_FEE), "Spillover payment failed");
                    
                    // Update upline stats
                    users[upline].spilloversReceived++;
                    users[upline].totalTeamSize++;
                    users[upline].spilloverTeam.push(_user);
                    users[upline].totalEarnings += SPILLOVER_FEE;
                    
                    // Update referrer stats
                    users[_referrer].spilloversGenerated++;
                    
                    // Add to upline's matrix
                    matrices[upline].spilloverReferrals.push(_user);
                    
                    emit SpilloverPaid(upline, _user, SPILLOVER_FEE);
                } else {
                    // If no upline, add to weekly fund
                    weeklyFund += SPILLOVER_FEE;
                }
            } else {
                // Pay spillover fee to referrer
                require(usdtToken.transfer(_referrer, SPILLOVER_FEE), "Spillover payment failed");
                users[_referrer].totalEarnings += SPILLOVER_FEE;
                
                emit SpilloverPaid(_referrer, _user, SPILLOVER_FEE);
            }
        } else {
            // Pay spillover fee to referrer
            require(usdtToken.transfer(_referrer, SPILLOVER_FEE), "Spillover payment failed");
            users[_referrer].totalEarnings += SPILLOVER_FEE;
            
            emit SpilloverPaid(_referrer, _user, SPILLOVER_FEE);
        }
    }
    
    /**
     * @dev Update user level based on direct referrals
     * @param _user Address of the user
     */
    function updateUserLevel(address _user) internal {
        uint256 directReferrals = users[_user].directReferrals;
        uint256 oldLevel = getUserLevel(_user);
        uint256 newLevel = 0;
        
        // Determine new level
        for (uint256 i = 0; i < levelThresholds.length; i++) {
            if (directReferrals >= levelThresholds[i]) {
                newLevel = i + 1;
            } else {
                break;
            }
        }
        
        if (oldLevel != newLevel) {
            emit UserLevelChanged(_user, oldLevel, newLevel);
        }
    }
    
    /**
     * @dev Get user's current level
     * @param _user Address of the user
     * @return level User's level (1-10)
     */
    function getUserLevel(address _user) public view returns (uint256) {
        uint256 directReferrals = users[_user].directReferrals;
        uint256 level = 0;
        
        for (uint256 i = 0; i < levelThresholds.length; i++) {
            if (directReferrals >= levelThresholds[i]) {
                level = i + 1;
            } else {
                break;
            }
        }
        
        return level;
    }
    
    /**
     * @dev Distribute weekly fund to active members
     * Can be called by anyone once the distribution time is reached
     */
    function distributeWeeklyFund() external nonReentrant {
        require(block.timestamp >= nextDistributionTime, "Distribution time not reached");
        require(weeklyFund > 0, "No funds to distribute");
        require(activeMembers[currentWeek].length > 0, "No active members");
        
        uint256 totalDistributed = 0;
        uint256 membersCount = activeMembers[currentWeek].length;
        
        // Calculate total shares
        uint256[] memory levelCounts = new uint256[](levelThresholds.length);
        uint256[] memory levelShares = new uint256[](levelThresholds.length);
        
        for (uint256 i = 0; i < membersCount; i++) {
            address member = activeMembers[currentWeek][i];
            uint256 level = getUserLevel(member);
            
            if (level > 0) {
                levelCounts[level - 1]++;
            }
        }
        
        // Calculate shares per level
        for (uint256 i = 0; i < levelThresholds.length; i++) {
            if (levelCounts[i] > 0) {
                uint256 levelAmount = (weeklyFund * levelPercentages[i]) / 100;
                levelShares[i] = levelCounts[i] > 0 ? levelAmount / levelCounts[i] : 0;
                totalDistributed += levelAmount;
            }
        }
        
        // Distribute to members
        for (uint256 i = 0; i < membersCount; i++) {
            address member = activeMembers[currentWeek][i];
            uint256 level = getUserLevel(member);
            
            if (level > 0 && levelShares[level - 1] > 0) {
                uint256 share = levelShares[level - 1];
                require(usdtToken.transfer(member, share), "Weekly distribution payment failed");
                
                users[member].weeklyEarnings += share;
                users[member].totalEarnings += share;
            }
        }
        
        // Reset weekly fund
        weeklyFund = 0;
        
        // Set next distribution time
        nextDistributionTime = block.timestamp + DISTRIBUTION_INTERVAL;
        
        // Increment week
        currentWeek++;
        
        emit WeeklyFundDistributed(currentWeek - 1, totalDistributed, membersCount);
    }
    
    /**
     * @dev Mark user as active for the current week
     * Users need to refer at least 1 person per week to be active
     */
    function markActive() external {
        require(users[msg.sender].userAddress != address(0), "User not found");
        require(users[msg.sender].lastActiveTime < nextDistributionTime - DISTRIBUTION_INTERVAL, "Already active this week");
        
        users[msg.sender].isActive = true;
        users[msg.sender].lastActiveTime = block.timestamp;
        
        // Add to active members for this week
        activeMembers[currentWeek].push(msg.sender);
    }
    
    /**
     * @dev Get user's team information
     * @param _user Address of the user
     * @return directTeam Array of direct referrals
     * @return spilloverTeam Array of spillover referrals
     */
    function getUserTeam(address _user) external view returns (address[] memory, address[] memory) {
        return (users[_user].directTeam, users[_user].spilloverTeam);
    }
    
    /**
     * @dev Get user's statistics
     * @param _user Address of the user
     * @return directReferrals Number of direct referrals
     * @return totalTeamSize Total team size
     * @return totalEarnings Total earnings
     * @return weeklyEarnings Weekly earnings
     * @return level Current level
     */
    function getUserStats(address _user) external view returns (
        uint256 directReferrals,
        uint256 totalTeamSize,
        uint256 totalEarnings,
        uint256 weeklyEarnings,
        uint256 level
    ) {
        User memory user = users[_user];
        return (
            user.directReferrals,
            user.totalTeamSize,
            user.totalEarnings,
            user.weeklyEarnings,
            getUserLevel(_user)
        );
    }
    
    /**
     * @dev Get time until next distribution
     * @return timeLeft Time left in seconds
     */
    function getTimeUntilNextDistribution() external view returns (uint256) {
        if (block.timestamp >= nextDistributionTime) {
            return 0;
        }
        return nextDistributionTime - block.timestamp;
    }
    
    /**
     * @dev Update USDT token address (in case of migration)
     * @param _newUsdtToken New USDT token address
     */
    function updateUsdtToken(address _newUsdtToken) external onlyOwner {
        usdtToken = IERC20(_newUsdtToken);
    }
    
    /**
     * @dev Emergency withdraw function (only owner)
     * @param _token Token address to withdraw
     * @param _amount Amount to withdraw
     */
    function emergencyWithdraw(address _token, uint256 _amount) external onlyOwner {
        IERC20(_token).transfer(owner(), _amount);
    }
}
