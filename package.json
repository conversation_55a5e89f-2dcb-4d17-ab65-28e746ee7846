{"name": "eternal-income-matrix", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@openzeppelin/contracts": "^4.9.3", "ethers": "^5.7.2", "framer-motion": "^10.16.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.16.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.29", "stylelint": "^16.19.1", "stylelint-config-standard": "^38.0.0", "tailwindcss": "^3.3.3", "vite": "^4.4.5"}}