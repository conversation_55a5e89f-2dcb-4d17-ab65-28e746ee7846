import { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { motion } from 'framer-motion'

// Web3 Provider
import { Web3Provider } from './hooks/useWeb3.jsx'
import eternalLogo from './assets/images/Eternal logo r.png'

// Components
import Navbar from './components/Navbar'
import Footer from './components/Footer'

// Pages
import Home from './pages/Home'
import About from './pages/About'
import HowItWorks from './pages/HowItWorks'
import IncomePlan from './pages/IncomePlan'
import SmartContract from './pages/SmartContract'
import ConnectWallet from './pages/ConnectWallet'
import Dashboard from './pages/Dashboard'
import FAQ from './pages/FAQ'

function App() {
  const [showIntro, setShowIntro] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)
  const [isDashboard, setIsDashboard] = useState(false)

  // Initial loading animation
  useEffect(() => {
    const timer = setTimeout(() => {
      setInitialLoading(false)
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  // Check if current route is dashboard
  useEffect(() => {
    const checkRoute = () => {
      const path = window.location.pathname
      setIsDashboard(path.includes('/dashboard'))
    }

    checkRoute()

    // Listen for route changes
    window.addEventListener('popstate', checkRoute)
    return () => window.removeEventListener('popstate', checkRoute)
  }, [])



  if (showIntro) {
    return (
      <div className="h-screen w-screen flex items-center justify-center bg-[#000000] relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-b from-[#1A1A1A] to-[#000000] opacity-80"></div>
        </div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center relative z-10"
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 1, type: "spring", bounce: 0.4 }}
            className="mb-8"
          >
            <img
              src={eternalLogo}
              alt="ETERNAL Logo"
              className="h-32 md:h-40 w-auto mx-auto"
              style={{ background: 'transparent' }}
            />
          </motion.div>

          <motion.h1
            className="text-4xl md:text-6xl font-orbitron font-bold text-primary neon-text-red mb-4"
            animate={{
              scale: [1, 1.05, 1],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          >
            ETERNAL
          </motion.h1>

          <motion.p
            className="text-xl md:text-2xl font-orbitron text-contrast mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          >
            INCOME MATRIX SYSTEM
          </motion.p>

          <motion.div
            className="mt-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 1 }}
          >
            <div className="w-12 h-12 border-t-2 border-r-2 border-primary rounded-full animate-spin mx-auto"></div>
          </motion.div>
        </motion.div>
      </div>
    )
  }



  return (
    <Web3Provider>
      <Router>
        <div className="min-h-screen flex flex-col bg-background bg-animated relative overflow-x-hidden">
          <div className="absolute inset-0 z-0">
            <div className="absolute inset-0 opacity-5"></div>
          </div>

          {!isDashboard && <Navbar />}
          <main className={`flex-grow relative z-10 ${!isDashboard ? 'pt-20' : ''} overflow-x-hidden`}>
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/about" element={<About />} />
              <Route path="/how-it-works" element={<HowItWorks />} />
              <Route path="/income-plan" element={<IncomePlan />} />
              <Route path="/smart-contract" element={<SmartContract />} />
              <Route path="/connect-wallet" element={<ConnectWallet />} />
              <Route path="/dashboard/*" element={<Dashboard />} />
              <Route path="/faq" element={<FAQ />} />
            </Routes>
          </main>
          {!isDashboard && <Footer />}
        </div>
      </Router>
    </Web3Provider>
  )
}

export default App
