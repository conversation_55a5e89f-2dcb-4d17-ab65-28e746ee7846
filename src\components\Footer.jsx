import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import eternalLogo from '../assets/images/Eternal logo r.png'

const Footer = () => {
  return (
    <footer className="bg-[#1A1A1A]/80 backdrop-blur-md border-t border-primary/20 py-12 relative z-10">
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-t from-primary/5 via-transparent to-transparent"></div>
        <div className="absolute inset-0 opacity-5"></div>
      </div>

      <div className="site-container relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <div className="flex items-center mb-4">
              <div className="relative logo-container mr-2">
                <img
                  src={eternalLogo}
                  alt="ETERNAL Logo"
                  className="h-10 w-auto relative logo-image"
                  style={{ background: 'transparent' }}
                />
              </div>
              <h3 className="text-xl font-orbitron font-bold text-accent neon-glow">ETERNAL</h3>
            </div>
            <p className="text-contrast text-sm">
              The revolutionary income matrix system with recursive spillover technology.
              Join with just $6 and start earning weekly passive income.
            </p>
          </div>

          <div>
            <h4 className="text-lg font-orbitron font-bold text-accent mb-6">Quick Links</h4>
            <ul className="space-y-3">
              <li>
                <Link to="/about" className="text-contrast hover:text-accent transition-all duration-300 flex items-center group">
                  <span className="w-0 h-0.5 bg-primary group-hover:w-4 transition-all duration-300 mr-0 group-hover:mr-2"></span>
                  About ETERNAL
                </Link>
              </li>
              <li>
                <Link to="/how-it-works" className="text-contrast hover:text-accent transition-all duration-300 flex items-center group">
                  <span className="w-0 h-0.5 bg-primary group-hover:w-4 transition-all duration-300 mr-0 group-hover:mr-2"></span>
                  How It Works
                </Link>
              </li>
              <li>
                <Link to="/income-plan" className="text-contrast hover:text-accent transition-all duration-300 flex items-center group">
                  <span className="w-0 h-0.5 bg-primary group-hover:w-4 transition-all duration-300 mr-0 group-hover:mr-2"></span>
                  Income Plan
                </Link>
              </li>
              <li>
                <Link to="/smart-contract" className="text-contrast hover:text-accent transition-all duration-300 flex items-center group">
                  <span className="w-0 h-0.5 bg-primary group-hover:w-4 transition-all duration-300 mr-0 group-hover:mr-2"></span>
                  Smart Contract
                </Link>
              </li>
              <li>
                <Link to="/faq" className="text-contrast hover:text-accent transition-all duration-300 flex items-center group">
                  <span className="w-0 h-0.5 bg-primary group-hover:w-4 transition-all duration-300 mr-0 group-hover:mr-2"></span>
                  FAQ
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-orbitron font-bold text-accent mb-6">Connect With Us</h4>
            <div className="flex space-x-4">
              <motion.a
                href="#"
                className="text-contrast hover:text-accent transition-all duration-300 bg-primary/10 p-3 rounded-full hover:bg-primary/20"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" />
                </svg>
              </motion.a>

              <motion.a
                href="#"
                className="text-contrast hover:text-accent transition-all duration-300 bg-primary/10 p-3 rounded-full hover:bg-primary/20"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                </svg>
              </motion.a>

              <motion.a
                href="#"
                className="text-contrast hover:text-accent transition-all duration-300 bg-primary/10 p-3 rounded-full hover:bg-primary/20"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" />
                </svg>
              </motion.a>

              <motion.a
                href="#"
                className="text-contrast hover:text-accent transition-all duration-300 bg-primary/10 p-3 rounded-full hover:bg-primary/20"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" />
                </svg>
              </motion.a>

              <motion.a
                href="#"
                className="text-contrast hover:text-accent transition-all duration-300 bg-primary/10 p-3 rounded-full hover:bg-primary/20"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z" />
                </svg>
              </motion.a>
            </div>

            <div className="mt-8">
              <motion.a
                href="#"
                className="text-contrast hover:text-accent transition-all duration-300 flex items-center bg-primary/10 p-3 rounded-md hover:bg-primary/20 border border-primary/30"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <svg className="h-5 w-5 mr-3 text-primary" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm.14 19.018c-.237 0-.47-.01-.7-.03-1.595-.132-2.603-1.176-2.603-2.475 0-.258.104-.986.198-1.527l.1-.476c.413-1.585.7-2.11.7-2.11h4.61s.287.525.7 2.11l.1.476c.094.54.198 1.27.198 1.527 0 1.3-1.007 2.344-2.603 2.475-.23.02-.463.03-.7.03zm0-15.036c-3.302 0-5.98 2.678-5.98 5.98 0 2.087 1.07 3.925 2.693 5.003.372-1.624 2.05-2.833 3.973-2.833s3.6 1.21 3.973 2.833c1.623-1.078 2.693-2.916 2.693-5.003 0-3.302-2.678-5.98-5.98-5.98z" />
                </svg>
                Join our Telegram Community
              </motion.a>
            </div>

            <div className="mt-4">
              <motion.a
                href="/connect-wallet"
                className="text-white hover:text-white transition-all duration-300 flex items-center bg-primary p-3 rounded-md hover:bg-primary/90 border border-primary/50 font-orbitron font-bold"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Connect Wallet & Join
              </motion.a>
            </div>
          </div>
        </div>

        <div className="mt-12 border-t border-primary/20 pt-8 text-center">
          <motion.p
            className="text-sm text-contrast/80"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            &copy; {new Date().getFullYear()} <span className="text-primary">ETERNAL</span> Income Matrix System. All rights reserved.
          </motion.p>
          <motion.p
            className="text-xs text-contrast/60 mt-2"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Powered by Binance Smart Chain
          </motion.p>
        </div>
      </div>
    </footer>
  )
}

export default Footer
