import { useState, useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import eternalLogo from '../assets/images/Eternal logo r.png'

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false)
  const location = useLocation()

  const navLinks = [
    { name: 'Home', path: '/' },
    { name: 'About', path: '/about' },
    { name: 'How It Works', path: '/how-it-works' },
    { name: 'Income Plan', path: '/income-plan' },
    { name: 'Smart Contract', path: '/smart-contract' },
    { name: 'FAQ', path: '/faq' },
  ]

  // Add scroll effect
  const [scrolled, setScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY
      if (offset > 50) {
        setScrolled(true)
      } else {
        setScrolled(false)
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const toggleMenu = () => {
    setIsOpen(!isOpen)
  }

  return (
    <motion.nav
      className={`fixed w-full z-50 transition-all duration-300 ${
        scrolled
          ? 'bg-background/90 backdrop-blur-md shadow-lg border-b border-primary/30'
          : 'bg-background border-b border-secondary/30'
      }`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Top glowing line */}
      <div className="absolute top-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-primary to-transparent opacity-70"></div>
      <div className="site-container">
        <div className="flex justify-between h-20">
          <div className="flex items-center">
            <Link to="/" className="flex-shrink-0 flex items-center">
              <motion.div
                className="flex items-center"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <motion.div
                  className="relative mr-3 logo-container"
                  animate={{
                    rotate: [0, 5, 0, -5, 0],
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <img
                    src={eternalLogo}
                    alt="ETERNAL Logo"
                    className="h-12 w-auto relative logo-image"
                    style={{ background: 'transparent' }}
                  />
                </motion.div>
                <div className="flex flex-col">
                  <motion.span
                    className="text-2xl font-orbitron font-bold text-accent neon-glow"
                    animate={{
                      textShadow: ["0 0 7px var(--accent-color)", "0 0 15px var(--accent-color)", "0 0 25px var(--accent-color)"],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                  >
                    ETERNAL
                  </motion.span>
                  <motion.span
                    className="text-xs font-orbitron tracking-widest text-contrast/70"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5, duration: 0.5 }}
                  >
                    INCOME MATRIX
                  </motion.span>
                </div>
              </motion.div>
            </Link>
          </div>

          {/* Desktop menu */}
          <div className="hidden md:flex items-center space-x-1">
            {navLinks.map((link, index) => (
              <motion.div
                key={link.name}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.3,
                  delay: index * 0.1,
                  type: "spring",
                  stiffness: 300
                }}
              >
                <Link
                  to={link.path}
                  className={`px-4 py-2 mx-1 text-sm font-orbitron font-medium rounded-md transition-all duration-300 relative overflow-hidden group ${
                    location.pathname === link.path
                      ? 'bg-primary/20 text-accent border border-primary/50 shadow-glow'
                      : 'text-contrast hover:text-accent hover:bg-secondary/20 border border-transparent'
                  }`}
                >
                  <span className="relative z-10">{link.name}</span>
                  <span className="absolute bottom-0 left-0 w-0 h-[2px] bg-primary group-hover:w-full transition-all duration-300"></span>
                  {location.pathname === link.path && (
                    <motion.span
                      className="absolute inset-0 bg-primary/10 -z-0"
                      animate={{
                        boxShadow: ["0 0 5px rgba(159, 0, 0, 0.3) inset", "0 0 15px rgba(159, 0, 0, 0.5) inset"]
                      }}
                      transition={{ duration: 1.5, repeat: Infinity, repeatType: "reverse" }}
                    />
                  )}
                </Link>
              </motion.div>
            ))}
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.3,
                delay: navLinks.length * 0.1,
                type: "spring",
                stiffness: 300
              }}
              className="relative"
            >
              <div className="absolute -inset-1 bg-gradient-to-r from-primary/30 to-primary/50 rounded-md blur-sm"></div>
              <Link
                to="/connect-wallet"
                className="relative ml-4 px-5 py-2 bg-primary text-white font-orbitron font-bold rounded-md hover:bg-primary/80 transition-all duration-300 shadow-glow border border-primary/50 flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Connect Wallet
              </Link>
            </motion.div>
          </div>

          {/* Mobile menu button */}
          <div className="flex md:hidden items-center">
            <motion.div
              className="relative"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: 0.5 }}
            >
              <div className={`absolute -inset-1 rounded-md blur-sm transition-opacity duration-300 ${isOpen ? 'bg-primary/30 opacity-100' : 'bg-secondary/20 opacity-70'}`}></div>
              <motion.button
                onClick={toggleMenu}
                className={`relative inline-flex items-center justify-center p-2 rounded-md focus:outline-none border transition-colors duration-300 ${
                  isOpen
                    ? 'text-accent border-primary/70 bg-primary/10'
                    : 'text-contrast hover:text-accent border-secondary/50 hover:border-primary/50'
                }`}
                whileTap={{ scale: 0.9 }}
                animate={isOpen ? { rotate: [0, 90, 180] } : { rotate: 0 }}
                transition={{ duration: 0.3 }}
              >
                <svg
                  className="h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  {isOpen ? (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  ) : (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  )}
                </svg>
              </motion.button>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="md:hidden bg-background/95 backdrop-blur-md border-t border-primary/20"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            {/* Animated background */}
            <div className="absolute inset-x-0 top-0 h-[1px] bg-gradient-to-r from-transparent via-primary to-transparent"></div>

            <div className="px-4 pt-6 pb-8 space-y-3 relative">
              {/* Decorative elements */}
              <div className="absolute top-0 left-0 w-[1px] h-full bg-gradient-to-b from-transparent via-primary/20 to-transparent"></div>
              <div className="absolute top-0 right-0 w-[1px] h-full bg-gradient-to-b from-transparent via-primary/20 to-transparent"></div>

              {navLinks.map((link, index) => (
                <motion.div
                  key={link.name}
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{
                    duration: 0.3,
                    delay: index * 0.05,
                    type: "spring",
                    stiffness: 300
                  }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link
                    to={link.path}
                    className={`block px-5 py-3 rounded-md text-base font-orbitron font-medium relative overflow-hidden group ${
                      location.pathname === link.path
                        ? 'bg-primary/20 text-accent border border-primary/50 shadow-glow'
                        : 'text-contrast hover:text-accent hover:bg-secondary/20 border border-secondary/10'
                    }`}
                    onClick={toggleMenu}
                  >
                    <span className="relative z-10">{link.name}</span>
                    <span className="absolute bottom-0 left-0 w-0 h-[2px] bg-primary group-hover:w-full transition-all duration-300"></span>
                    {location.pathname === link.path && (
                      <motion.span
                        className="absolute inset-0 bg-primary/10 -z-0"
                        animate={{
                          boxShadow: ["0 0 5px rgba(159, 0, 0, 0.3) inset", "0 0 15px rgba(159, 0, 0, 0.5) inset"]
                        }}
                        transition={{ duration: 1.5, repeat: Infinity, repeatType: "reverse" }}
                      />
                    )}
                  </Link>
                </motion.div>
              ))}

              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{
                  duration: 0.5,
                  delay: navLinks.length * 0.05 + 0.2,
                  type: "spring",
                  stiffness: 300
                }}
                className="pt-4 mt-4 border-t border-secondary/20"
              >
                <motion.div
                  whileTap={{ scale: 0.95 }}
                  className="relative"
                >
                  <div className="absolute -inset-1 bg-gradient-to-r from-primary/30 to-primary/50 rounded-md blur-sm"></div>
                  <Link
                    to="/connect-wallet"
                    className="relative block px-5 py-4 bg-primary text-white font-orbitron font-bold rounded-md hover:bg-primary/80 transition-all duration-300 text-center shadow-glow border border-primary/50 flex items-center justify-center"
                    onClick={toggleMenu}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    Connect Wallet
                  </Link>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.nav>
  )
}

export default Navbar
