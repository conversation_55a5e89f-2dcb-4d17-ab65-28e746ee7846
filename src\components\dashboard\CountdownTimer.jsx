import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const CountdownTimer = ({ timeUntilDistribution }) => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  useEffect(() => {
    // Calculate initial time left
    const calculateTimeLeft = () => {
      const days = Math.floor(timeUntilDistribution / 86400);
      const hours = Math.floor((timeUntilDistribution % 86400) / 3600);
      const minutes = Math.floor((timeUntilDistribution % 3600) / 60);
      const seconds = Math.floor(timeUntilDistribution % 60);

      return { days, hours, minutes, seconds };
    };

    setTimeLeft(calculateTimeLeft());

    // Set up interval to update countdown
    let timer = null;
    if (timeUntilDistribution > 0) {
      let secondsLeft = timeUntilDistribution;

      timer = setInterval(() => {
        secondsLeft -= 1;

        if (secondsLeft < 0) {
          clearInterval(timer);
          return;
        }

        const days = Math.floor(secondsLeft / 86400);
        const hours = Math.floor((secondsLeft % 86400) / 3600);
        const minutes = Math.floor((secondsLeft % 3600) / 60);
        const seconds = Math.floor(secondsLeft % 60);

        setTimeLeft({ days, hours, minutes, seconds });
      }, 1000);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [timeUntilDistribution]);

  const timeUnits = [
    { label: 'Days', value: timeLeft.days },
    { label: 'Hours', value: timeLeft.hours },
    { label: 'Minutes', value: timeLeft.minutes },
    { label: 'Seconds', value: timeLeft.seconds }
  ];

  return (
    <div className="flex space-x-3">
      {timeUnits.map((unit, index) => (
        <motion.div
          key={unit.label}
          className="flex flex-col items-center"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <div className="bg-[#1A1A1A] rounded-lg w-14 h-14 flex items-center justify-center mb-1 relative overflow-hidden border border-[#9F0000]/30">
            <div className="absolute inset-0 bg-[#FF2C2C]/5"></div>
            <span className="text-xl font-bold text-white relative z-10">
              {unit.value.toString().padStart(2, '0')}
            </span>
          </div>
          <span className="text-xs text-[#FFFFFF]">{unit.label}</span>
        </motion.div>
      ))}
    </div>
  );
};

export default CountdownTimer;
