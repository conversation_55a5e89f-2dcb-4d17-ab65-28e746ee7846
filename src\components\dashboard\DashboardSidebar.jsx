import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';

const DashboardSidebar = () => {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('overview');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  // Update active tab based on URL
  useEffect(() => {
    const path = location.pathname;
    if (path.includes('/dashboard/income')) {
      setActiveTab('income');
    } else if (path.includes('/dashboard/referrals')) {
      setActiveTab('referrals');
    } else if (path.includes('/dashboard/profile')) {
      setActiveTab('profile');
    } else {
      setActiveTab('overview');
    }
  }, [location]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth >= 768) {
        setIsMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Navigation items
  const navItems = [
    {
      id: 'overview',
      label: 'Dashboard',
      path: '/dashboard',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
        </svg>
      )
    },

    {
      id: 'income',
      label: 'Income',
      path: '/dashboard/income',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    {
      id: 'referrals',
      label: 'Referrals',
      path: '/dashboard/referrals',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      )
    },
    {
      id: 'profile',
      label: 'Profile',
      path: '/dashboard/profile',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      )
    }
  ];

  return (
    <>
      {/* Mobile menu button */}
      {isMobile && (
        <button
          className="fixed top-4 left-4 z-50 p-2 rounded-lg bg-[#1A1A1A] border border-[#9F0000]/50 text-white"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          {isMobileMenuOpen ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          )}
        </button>
      )}

      {/* Sidebar */}
      <motion.div
        className={`fixed top-0 left-0 h-full w-64 bg-[#0D0D0D] border-r border-[#9F0000]/30 shadow-[0_0_15px_rgba(159,0,0,0.2)] z-40 md:translate-x-0 ${
          isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
        } transition-transform duration-300 ease-in-out`}
        initial={{ x: isMobile ? -320 : 0 }}
        animate={{ x: isMobileMenuOpen || !isMobile ? 0 : -320 }}
      >
        {/* Logo and Title */}
        <div className="relative z-10 p-6 pb-8 border-b border-[#222]/50">
          <Link to="/" className="flex items-center justify-center md:justify-start">
            <motion.span
              className="text-2xl font-bold text-[#FF2C2C] relative"
              animate={{
                textShadow: ["0 0 7px #FF2C2C", "0 0 10px #FF2C2C", "0 0 21px #FF2C2C"],
                opacity: [1, 0.9, 1]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            >
              ETERNAL
              {/* Underline effect */}
              <span className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-[#FF2C2C] to-transparent"></span>
            </motion.span>
          </Link>
          <div className="mt-2 text-xs text-center md:text-left text-[#999]">
            Income Matrix System
          </div>
        </div>

        {/* Navigation */}
        <div className="p-4">
          <h3 className="text-xs uppercase text-[#999] font-semibold tracking-wider mb-4 px-2 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-[#FF2C2C]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
            Navigation
          </h3>

          <nav className="space-y-1">
            {navItems.map((item) => (
              <Link key={item.id} to={item.path}>
                <div
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-300 relative overflow-hidden ${
                    activeTab === item.id
                      ? 'bg-gradient-to-r from-[#9F0000]/30 to-[#FF2C2C]/10 text-white shadow-[0_0_15px_rgba(159,0,0,0.3)] border border-[#9F0000]'
                      : 'text-white/80 hover:bg-[#1A1A1A] hover:text-white hover:shadow-[0_0_10px_rgba(255,44,44,0.1)]'
                  }`}
                  onClick={() => {
                    setActiveTab(item.id);
                    if (isMobile) setIsMobileMenuOpen(false);
                  }}
                >
                  {/* Icon */}
                  <span className={`${activeTab === item.id ? 'text-[#FF2C2C]' : ''} transition-colors duration-300`}>
                    {item.icon}
                  </span>

                  {/* Label */}
                  <span className="font-medium">{item.label}</span>

                  {/* Active indicator */}
                  {activeTab === item.id && (
                    <motion.span
                      className="ml-auto h-2 w-2 rounded-full bg-[#FF2C2C]"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.3 }}
                    />
                  )}
                </div>
              </Link>
            ))}
          </nav>
        </div>

        {/* Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-6 border-t border-[#222]/50">
          <Link
            to="/connect-wallet"
            className="w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg bg-gradient-to-r from-[#9F0000]/30 to-[#FF2C2C]/10 text-white hover:from-[#9F0000]/40 hover:to-[#FF2C2C]/20 transition-all duration-300 border border-[#9F0000] shadow-[0_0_10px_rgba(159,0,0,0.2)]"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#FF2C2C]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            <span>Disconnect</span>
          </Link>

          <div className="mt-4 text-xs text-center text-[#999]">
            © 2023 Eternal Matrix
          </div>
        </div>
      </motion.div>
    </>
  );
};

export default DashboardSidebar;
