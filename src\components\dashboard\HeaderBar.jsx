import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import Eternal<PERSON><PERSON> from '../../assets/images/Eternal logo r.png';

const HeaderBar = ({ userData }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <motion.div
      className="bg-[#0D0D0D] border-b border-[#9F0000]/30 sticky top-0 z-50"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="max-w-7xl mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center">
              <div className="h-12 flex items-center justify-center overflow-hidden">
                <img
                  src={EternalLogo}
                  alt="Eternal Matrix Logo"
                  className="h-full object-contain"
                />
              </div>
            </Link>
          </div>

          {/* Center - Balance Display */}
          <div className="hidden md:block">
            <div className="bg-[#1A1A1A] rounded-lg px-6 py-2 border border-[#9F0000]/40 relative overflow-hidden">
              <div className="absolute -top-10 -left-10 w-20 h-20 bg-[#FF0044]/5 rounded-full blur-xl"></div>
              <div className="flex items-center">
                <div>
                  <div className="text-white/50 text-xs">Your Balance</div>
                  <div className="text-white text-xl font-bold">${userData?.balance?.toFixed(2) || '245.67'}</div>
                </div>
                <div className="ml-6 pl-6 border-l border-[#9F0000]/30">
                  <div className="text-white/50 text-xs">Next Distribution</div>
                  <div className="text-white text-sm font-medium">6d 17h 9m</div>
                </div>
              </div>
            </div>
          </div>

          {/* Right - Menu and Connect Wallet */}
          <div className="flex items-center space-x-4">
            {/* Mobile Menu Button */}
            <button
              className="md:hidden text-white/70 hover:text-[#FF0044] transition-colors"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-6">
              <NavLink to="/dashboard" label="Dashboard" active={true} />
              <NavLink to="/dashboard/income" label="Income" />
              <NavLink to="/dashboard/referrals" label="Referrals" />
              <NavLink to="/dashboard/matrix" label="Matrix" />
            </div>

            {/* Connect Wallet Button */}
            <motion.button
              className="bg-gradient-to-r from-[#9F0000] to-[#FF0044] text-white px-4 py-2 rounded-lg font-medium text-sm flex items-center shadow-lg"
              whileHover={{
                scale: 1.05,
                boxShadow: "0 0 15px rgba(255, 0, 68, 0.5)"
              }}
              whileTap={{ scale: 0.95 }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              {userData?.address ? 'Connected' : 'Connect Wallet'}
            </motion.button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <motion.div
          className="md:hidden bg-[#1A1A1A] border-t border-[#9F0000]/30"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.2 }}
        >
          <div className="px-4 py-3">
            <div className="flex flex-col space-y-2">
              <MobileNavLink to="/dashboard" label="Dashboard" active={true} />
              <MobileNavLink to="/dashboard/income" label="Income" />
              <MobileNavLink to="/dashboard/referrals" label="Referrals" />
              <MobileNavLink to="/dashboard/matrix" label="Matrix" />
              <MobileNavLink to="/dashboard/profile" label="Profile" />
              <MobileNavLink to="/dashboard/help" label="Help Center" />

              {/* Mobile Logo Display */}
              <div className="flex justify-center my-4">
                <img
                  src={EternalLogo}
                  alt="Eternal Matrix Logo"
                  className="h-10 object-contain"
                />
              </div>

              {/* Mobile Balance Display */}
              <div className="bg-[#0D0D0D] rounded-lg p-3 mt-2 border border-[#9F0000]/30">
                <div className="flex justify-between items-center">
                  <div>
                    <div className="text-white/50 text-xs">Your Balance</div>
                    <div className="text-white text-lg font-bold">${userData?.balance?.toFixed(2) || '245.67'}</div>
                  </div>
                  <div>
                    <div className="text-white/50 text-xs">Next Distribution</div>
                    <div className="text-white text-sm">6d 17h 9m</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};

// Navigation Link Component
const NavLink = ({ to, label, active = false }) => (
  <Link
    to={to}
    className={`text-sm font-medium relative ${active ? 'text-[#FF0044]' : 'text-white/70 hover:text-white'}`}
  >
    {label}
    {active && (
      <motion.div
        className="absolute -bottom-3.5 left-0 right-0 h-0.5 bg-[#FF0044]"
        layoutId="activeNavIndicator"
      />
    )}
  </Link>
);

// Mobile Navigation Link Component
const MobileNavLink = ({ to, label, active = false }) => (
  <Link
    to={to}
    className={`text-sm py-2 px-3 rounded-lg ${active ? 'bg-[#FF0044]/10 text-[#FF0044]' : 'text-white/70 hover:bg-white/5 hover:text-white'}`}
  >
    {label}
  </Link>
);

export default HeaderBar;
