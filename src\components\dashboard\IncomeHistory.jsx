import React, { useState } from 'react';
import { motion } from 'framer-motion';

const IncomeHistory = ({ userData, timeframe }) => {
  const [hoveredBar, setHoveredBar] = useState(null);

  // Sample data for the chart
  const chartData = {
    weekly: [
      { week: 'Week 1', direct: 25, spillover: 15, matrix: 10, total: 50, date: '2023-06-25' },
      { week: 'Week 2', direct: 30, spillover: 20, matrix: 15, total: 65, date: '2023-07-02' },
      { week: 'Week 3', direct: 40, spillover: 25, matrix: 20, total: 85, date: '2023-07-09' },
      { week: 'Week 4', direct: 35, spillover: 30, matrix: 25, total: 90, date: '2023-07-16' },
      { week: 'Week 5', direct: 45, spillover: 35, matrix: 30, total: 110, date: '2023-07-23' },
      { week: 'Week 6', direct: 50, spillover: 40, matrix: 35, total: 125, date: '2023-07-30' },
      { week: 'Week 7', direct: 55, spillover: 45, matrix: 40, total: 140, date: '2023-08-06' },
      { week: 'Week 8', direct: 60, spillover: 50, matrix: 45, total: 155, date: '2023-08-13' },
    ],
    monthly: [
      { month: 'Jan', direct: 100, spillover: 80, matrix: 70, total: 250, date: '2023-01-31' },
      { month: 'Feb', direct: 120, spillover: 90, matrix: 80, total: 290, date: '2023-02-28' },
      { month: 'Mar', direct: 140, spillover: 100, matrix: 90, total: 330, date: '2023-03-31' },
      { month: 'Apr', direct: 160, spillover: 110, matrix: 100, total: 370, date: '2023-04-30' },
      { month: 'May', direct: 180, spillover: 120, matrix: 110, total: 410, date: '2023-05-31' },
      { month: 'Jun', direct: 200, spillover: 130, matrix: 120, total: 450, date: '2023-06-30' },
      { month: 'Jul', direct: 220, spillover: 140, matrix: 130, total: 490, date: '2023-07-31' },
      { month: 'Aug', direct: 240, spillover: 150, matrix: 140, total: 530, date: '2023-08-13' },
    ]
  };

  const data = timeframe === 'weekly' ? chartData.weekly : chartData.monthly;
  const maxValue = Math.max(...data.map(item => item.total)) * 1.2; // Add 20% padding

  // Format currency
  const formatCurrency = (value) => {
    return `$${value.toFixed(2)}`;
  };

  // Calculate total earnings
  const totalEarnings = data.reduce((sum, item) => sum + item.total, 0);

  // Calculate average earnings
  const averageEarnings = totalEarnings / data.length;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-chart-purple rounded-sm mr-1"></div>
            <span className="text-white/70 text-xs">Direct</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-chart-blue rounded-sm mr-1"></div>
            <span className="text-white/70 text-xs">Spillover</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-chart-green rounded-sm mr-1"></div>
            <span className="text-white/70 text-xs">Matrix</span>
          </div>
        </div>

        <div className="text-xs text-white/50">
          Total: <span className="text-highlight font-medium">{formatCurrency(totalEarnings)}</span>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 gap-3 mb-4">
        <div className="bg-background rounded-lg p-3 border border-primary/20">
          <div className="text-white/70 text-xs mb-1">Average {timeframe === 'weekly' ? 'Weekly' : 'Monthly'}</div>
          <div className="text-white text-lg font-bold">{formatCurrency(averageEarnings)}</div>
        </div>

        <div className="bg-background rounded-lg p-3 border border-primary/20">
          <div className="text-white/70 text-xs mb-1">Best {timeframe === 'weekly' ? 'Week' : 'Month'}</div>
          <div className="text-white text-lg font-bold">{formatCurrency(Math.max(...data.map(item => item.total)))}</div>
        </div>
      </div>

      {/* Chart */}
      <div className="bg-background rounded-lg p-4 border border-primary/20 relative">
        {/* Horizontal grid lines */}
        <div className="absolute inset-x-0 top-10 bottom-8 flex flex-col justify-between">
          {[0, 1, 2, 3, 4].map((_, i) => (
            <div key={i} className="border-t border-white/5 w-full h-0"></div>
          ))}
        </div>

        <div className="h-48 md:h-64 relative">
          <div className="absolute inset-0 flex items-end justify-between">
            {data.map((item, index) => (
              <div
                key={index}
                className="flex flex-col items-center w-full"
                onMouseEnter={() => setHoveredBar(index)}
                onMouseLeave={() => setHoveredBar(null)}
              >
                <div className="w-full flex items-end justify-center h-40 md:h-56 space-x-0.5 md:space-x-1 relative">
                  {/* Tooltip */}
                  {hoveredBar === index && (
                    <motion.div
                      className="absolute -top-16 left-1/2 transform -translate-x-1/2 bg-background border border-primary/30 rounded-lg p-2 shadow-lg z-10 w-36"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="text-xs text-white/70 mb-1">
                        {timeframe === 'weekly' ? `Week ${index + 1}` : item.month} ({new Date(item.date).toLocaleDateString()})
                      </div>
                      <div className="flex justify-between text-xs">
                        <span className="text-chart-purple">Direct:</span>
                        <span className="text-white">${item.direct}</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span className="text-chart-blue">Spillover:</span>
                        <span className="text-white">${item.spillover}</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span className="text-chart-green">Matrix:</span>
                        <span className="text-white">${item.matrix}</span>
                      </div>
                      <div className="flex justify-between text-xs font-medium mt-1 pt-1 border-t border-white/10">
                        <span className="text-white">Total:</span>
                        <span className="text-highlight">${item.total}</span>
                      </div>

                      {/* Arrow */}
                      <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-background"></div>
                    </motion.div>
                  )}

                  <motion.div
                    className={`w-2 md:w-3 bg-chart-purple rounded-t ${hoveredBar === index ? 'opacity-100' : 'opacity-80'}`}
                    initial={{ height: 0 }}
                    animate={{ height: `${(item.direct / maxValue) * 100}%` }}
                    transition={{ duration: 0.5, delay: index * 0.05 }}
                    whileHover={{ opacity: 1 }}
                  />
                  <motion.div
                    className={`w-2 md:w-3 bg-chart-blue rounded-t ${hoveredBar === index ? 'opacity-100' : 'opacity-80'}`}
                    initial={{ height: 0 }}
                    animate={{ height: `${(item.spillover / maxValue) * 100}%` }}
                    transition={{ duration: 0.5, delay: index * 0.05 + 0.1 }}
                    whileHover={{ opacity: 1 }}
                  />
                  <motion.div
                    className={`w-2 md:w-3 bg-chart-green rounded-t ${hoveredBar === index ? 'opacity-100' : 'opacity-80'}`}
                    initial={{ height: 0 }}
                    animate={{ height: `${(item.matrix / maxValue) * 100}%` }}
                    transition={{ duration: 0.5, delay: index * 0.05 + 0.2 }}
                    whileHover={{ opacity: 1 }}
                  />
                </div>
                <div className="text-white/50 text-[10px] md:text-xs mt-1">
                  {timeframe === 'weekly' ? `W${index + 1}` : item.month.substring(0, 3)}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Bottom axis */}
        <div className="h-px bg-white/10 w-full mt-1"></div>

        {/* Y-axis labels */}
        <div className="absolute left-0 top-10 bottom-8 flex flex-col justify-between items-start">
          {[4, 3, 2, 1, 0].map((i) => (
            <div key={i} className="text-[10px] text-white/30">
              ${Math.round(maxValue * i / 4)}
            </div>
          ))}
        </div>
      </div>

      {/* Income Breakdown */}
      <div className="mt-4 bg-background rounded-lg p-3 border border-primary/20">
        <h4 className="text-white text-xs font-medium mb-2">Income Breakdown</h4>

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-chart-purple rounded-full mr-2"></div>
              <span className="text-white/70 text-xs">Direct Referrals</span>
            </div>
            <div className="text-white text-xs font-medium">
              {formatCurrency(data.reduce((sum, item) => sum + item.direct, 0))}
            </div>
          </div>

          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-chart-blue rounded-full mr-2"></div>
              <span className="text-white/70 text-xs">Spillover Bonuses</span>
            </div>
            <div className="text-white text-xs font-medium">
              {formatCurrency(data.reduce((sum, item) => sum + item.spillover, 0))}
            </div>
          </div>

          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-chart-green rounded-full mr-2"></div>
              <span className="text-white/70 text-xs">Matrix Payouts</span>
            </div>
            <div className="text-white text-xs font-medium">
              {formatCurrency(data.reduce((sum, item) => sum + item.matrix, 0))}
            </div>
          </div>

          <div className="flex justify-between items-center pt-2 border-t border-white/10 mt-2">
            <div className="text-white text-xs font-medium">Total Earnings</div>
            <div className="text-highlight text-xs font-medium">
              {formatCurrency(totalEarnings)}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default IncomeHistory;
