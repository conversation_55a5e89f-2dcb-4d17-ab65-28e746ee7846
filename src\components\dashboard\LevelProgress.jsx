import React from 'react';
import { motion } from 'framer-motion';

const LevelProgress = ({ userData }) => {
  // Current level and progress data
  const currentLevel = 3;
  const nextLevel = 4;
  const progress = 50; // percentage
  const requiredReferrals = 10;
  const currentReferrals = 5;

  // Level benefits
  const levelBenefits = [
    { level: 1, benefit: "Basic matrix access" },
    { level: 2, benefit: "10% referral bonus" },
    { level: 3, benefit: "Weekly pool share" },
    { level: 4, benefit: "15% referral bonus" },
    { level: 5, benefit: "Team override bonus" }
  ];

  // Current and next level benefits
  const currentBenefits = levelBenefits.filter(b => b.level <= currentLevel);
  const nextBenefit = levelBenefits.find(b => b.level === nextLevel);

  return (
    <div>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <motion.div
            className="bg-primary/20 text-primary font-bold rounded-full w-8 h-8 flex items-center justify-center text-sm"
            whileHover={{ scale: 1.1 }}
          >
            {currentLevel}
          </motion.div>
          <div className="mx-2 flex-1 relative">
            <div className="h-1 bg-background rounded-full overflow-hidden w-16">
              <motion.div
                className="h-full bg-primary"
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 1, ease: "easeOut" }}
              />
            </div>
            <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 text-white/30 text-xs">
              {progress}%
            </div>
          </div>
          <motion.div
            className="bg-background text-white/50 font-bold rounded-full w-8 h-8 flex items-center justify-center text-sm border border-primary/20"
            whileHover={{ scale: 1.1, borderColor: "rgba(255, 0, 68, 0.5)" }}
          >
            {nextLevel}
          </motion.div>
        </div>
        <div className="text-white/70 text-xs font-medium bg-background px-2 py-1 rounded border border-primary/20">
          Level {currentLevel}
        </div>
      </div>

      {/* Progress bar */}
      <div className="h-3 bg-background rounded-full overflow-hidden mb-4 relative">
        <motion.div
          className="h-full bg-gradient-to-r from-primary/70 to-primary"
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 1, ease: "easeOut" }}
        />

        {/* Progress markers */}
        {[20, 40, 60, 80].map((marker, index) => (
          <div
            key={index}
            className={`absolute top-0 bottom-0 w-0.5 ${marker <= progress ? 'bg-white/30' : 'bg-white/10'}`}
            style={{ left: `${marker}%` }}
          />
        ))}
      </div>

      <div className="flex justify-between items-start mb-4">
        <div className="text-xs text-white/70 bg-background p-2 rounded border border-primary/20 flex-1 mr-2">
          <div className="font-medium text-white mb-1">Current Progress</div>
          <div>You have {currentReferrals} of {requiredReferrals} direct referrals needed for Level {nextLevel}.</div>
        </div>

        <div className="text-xs text-white/70 bg-background p-2 rounded border border-primary/20 flex-1">
          <div className="font-medium text-white mb-1">Next Benefit</div>
          <div>Level {nextLevel}: {nextBenefit?.benefit}</div>
        </div>
      </div>

      {/* Current benefits */}
      <div className="text-xs text-white/70">
        <div className="font-medium text-white mb-1">Your Current Benefits:</div>
        <ul className="space-y-1">
          {currentBenefits.map((benefit, index) => (
            <motion.li
              key={index}
              className="flex items-start"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span>Level {benefit.level}: {benefit.benefit}</span>
            </motion.li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default LevelProgress;
