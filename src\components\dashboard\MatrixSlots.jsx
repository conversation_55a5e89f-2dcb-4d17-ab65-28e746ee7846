import React from 'react';
import { motion } from 'framer-motion';

const MatrixSlots = ({ userData }) => {
  // Matrix data - simplified representation of user's matrix positions
  const matrixData = {
    activePositions: 3,
    totalPositions: 12,
    filledSlots: [
      { id: 1, level: 1, status: 'active', profit: '30 BUSD', partners: 3 },
      { id: 2, level: 1, status: 'active', profit: '20 BUSD', partners: 2 },
      { id: 3, level: 2, status: 'active', profit: '10 BUSD', partners: 1 },
      { id: 4, level: 2, status: 'pending', profit: '0 BUSD', partners: 0 },
      { id: 5, level: 3, status: 'pending', profit: '0 BUSD', partners: 0 },
    ],
    // Levels represent different matrix tiers
    levels: [
      { id: 1, name: 'Level 1', price: '10 BUSD', color: '#FF2C2C', filled: 2, total: 3 },
      { id: 2, name: 'Level 2', price: '20 BUSD', color: '#9F0000', filled: 1, total: 3 },
      { id: 3, name: 'Level 3', price: '40 BUSD', color: '#00FFAA', filled: 0, total: 3 },
      { id: 4, name: 'Level 4', price: '80 BUSD', color: '#4169E1', filled: 0, total: 3 },
    ]
  };

  return (
    <div className="mb-8">
      {/* Matrix Overview */}
      <div className="bg-[#1A1A1A] rounded-xl p-6 border border-[#9F0000]/30 shadow-[0_0_15px_rgba(159,0,0,0.2)] relative overflow-hidden mb-6">
        {/* Background effects */}
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#FF2C2C] to-transparent opacity-50"></div>
        <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#FF2C2C] to-transparent opacity-50"></div>
        <div className="absolute -top-20 -right-20 w-40 h-40 bg-[#FF2C2C]/5 rounded-full blur-3xl"></div>
        
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-bold text-white">Matrix Positions</h3>
            <div className="flex items-center space-x-2">
              <span className="text-[#999] text-sm">Active: {matrixData.activePositions}/{matrixData.totalPositions}</span>
              <div className="w-24 h-2 bg-[#0D0D0D] rounded-full overflow-hidden">
                <div 
                  className="h-full bg-[#FF2C2C]" 
                  style={{ width: `${(matrixData.activePositions / matrixData.totalPositions) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
          
          {/* Matrix Levels */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {matrixData.levels.map((level, index) => (
              <motion.div
                key={level.id}
                className="bg-[#0D0D0D] rounded-lg p-4 border border-[#9F0000]/20"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <div className="flex justify-between items-center mb-2">
                  <span className="text-white font-medium">{level.name}</span>
                  <span className="text-[#999] text-sm">{level.price}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-full h-2 bg-[#1A1A1A] rounded-full overflow-hidden">
                    <div 
                      className="h-full" 
                      style={{ 
                        width: `${(level.filled / level.total) * 100}%`,
                        backgroundColor: level.color 
                      }}
                    ></div>
                  </div>
                  <span className="text-[#999] text-xs whitespace-nowrap">{level.filled}/{level.total}</span>
                </div>
              </motion.div>
            ))}
          </div>
          
          {/* Active Slots */}
          <div>
            <h4 className="text-md font-medium text-white mb-4">Active Positions</h4>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left text-[#999] text-sm">
                    <th className="pb-3 font-medium">ID</th>
                    <th className="pb-3 font-medium">Level</th>
                    <th className="pb-3 font-medium">Status</th>
                    <th className="pb-3 font-medium">Partners</th>
                    <th className="pb-3 font-medium">Profit</th>
                    <th className="pb-3 font-medium"></th>
                  </tr>
                </thead>
                <tbody>
                  {matrixData.filledSlots.map((slot) => (
                    <tr key={slot.id} className="border-t border-[#9F0000]/20 hover:bg-[#1A1A1A] transition-colors duration-200">
                      <td className="py-3">#{slot.id}</td>
                      <td className="py-3">Level {slot.level}</td>
                      <td className="py-3">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          slot.status === 'active' 
                            ? 'bg-[#00FFAA]/20 text-[#00FFAA]' 
                            : 'bg-[#FF2C2C]/20 text-[#FF2C2C]'
                        }`}>
                          {slot.status}
                        </span>
                      </td>
                      <td className="py-3">{slot.partners}</td>
                      <td className="py-3 font-medium">{slot.profit}</td>
                      <td className="py-3 text-right">
                        <button className="text-[#FF2C2C] hover:text-white transition-colors text-sm">
                          View
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MatrixSlots;
