import React, { useState } from 'react';
import { motion } from 'framer-motion';

const ReferralLink = ({ userData }) => {
  const [copied, setCopied] = useState(false);
  const [activeTab, setActiveTab] = useState('link');
  const referralLink = userData.referralLink || 'https://eternal.matrix.io/ref/468';

  const handleCopy = () => {
    navigator.clipboard.writeText(referralLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // QR code SVG (simplified for demo)
  const QRCode = () => (
    <div className="bg-white p-3 rounded-lg mx-auto w-40 h-40 flex items-center justify-center">
      <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="10" y="10" width="20" height="20" fill="black"/>
        <rect x="30" y="10" width="10" height="10" fill="black"/>
        <rect x="50" y="10" width="10" height="10" fill="black"/>
        <rect x="70" y="10" width="10" height="10" fill="black"/>
        <rect x="90" y="10" width="20" height="20" fill="black"/>
        <rect x="10" y="30" width="10" height="10" fill="black"/>
        <rect x="30" y="30" width="10" height="10" fill="black"/>
        <rect x="50" y="30" width="20" height="10" fill="black"/>
        <rect x="90" y="30" width="10" height="10" fill="black"/>
        <rect x="10" y="50" width="10" height="20" fill="black"/>
        <rect x="30" y="50" width="20" height="10" fill="black"/>
        <rect x="60" y="50" width="10" height="10" fill="black"/>
        <rect x="80" y="50" width="10" height="10" fill="black"/>
        <rect x="100" y="50" width="10" height="10" fill="black"/>
        <rect x="10" y="80" width="10" height="10" fill="black"/>
        <rect x="30" y="80" width="10" height="10" fill="black"/>
        <rect x="50" y="80" width="10" height="10" fill="black"/>
        <rect x="70" y="80" width="10" height="10" fill="black"/>
        <rect x="90" y="80" width="10" height="10" fill="black"/>
        <rect x="10" y="90" width="20" height="20" fill="black"/>
        <rect x="40" y="90" width="10" height="20" fill="black"/>
        <rect x="60" y="90" width="10" height="10" fill="black"/>
        <rect x="80" y="90" width="10" height="10" fill="black"/>
        <rect x="100" y="90" width="10" height="10" fill="black"/>
      </svg>
    </div>
  );

  // Social sharing options
  const socialOptions = [
    {
      name: 'Facebook',
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
            </svg>,
      color: '#1877F2',
      url: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(referralLink)}`
    },
    {
      name: 'Twitter',
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
            </svg>,
      color: '#1DA1F2',
      url: `https://twitter.com/intent/tweet?url=${encodeURIComponent(referralLink)}&text=${encodeURIComponent('Join me on Eternal Income Matrix!')}`
    },
    {
      name: 'WhatsApp',
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.297-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
            </svg>,
      color: '#25D366',
      url: `https://wa.me/?text=${encodeURIComponent('Join me on Eternal Income Matrix! ' + referralLink)}`
    },
    {
      name: 'LinkedIn',
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z"/>
            </svg>,
      color: '#0077B5',
      url: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(referralLink)}`
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Tabs */}
      <div className="flex border-b border-white/10 mb-4">
        <button
          className={`pb-2 px-4 text-sm font-medium ${activeTab === 'link' ? 'text-primary border-b-2 border-primary' : 'text-white/50'}`}
          onClick={() => setActiveTab('link')}
        >
          Link
        </button>
        <button
          className={`pb-2 px-4 text-sm font-medium ${activeTab === 'qr' ? 'text-primary border-b-2 border-primary' : 'text-white/50'}`}
          onClick={() => setActiveTab('qr')}
        >
          QR Code
        </button>
        <button
          className={`pb-2 px-4 text-sm font-medium ${activeTab === 'social' ? 'text-primary border-b-2 border-primary' : 'text-white/50'}`}
          onClick={() => setActiveTab('social')}
        >
          Share
        </button>
      </div>

      {/* Link Tab */}
      {activeTab === 'link' && (
        <div>
          <div className="bg-background rounded-lg p-3 border border-primary/20 mb-3 relative">
            <input
              type="text"
              value={referralLink}
              readOnly
              className="w-full bg-transparent text-primary text-sm border-none focus:outline-none"
            />
            <button
              onClick={handleCopy}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-primary transition-colors"
            >
              {copied ? (
                <motion.div
                  initial={{ scale: 0.8 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-highlight" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </motion.div>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z" />
                  <path d="M3 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L10.414 13H15v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5zM15 11h2a1 1 0 110 2h-2v-2z" />
                </svg>
              )}
            </button>
          </div>

          <div className="text-center text-white/50 text-xs mt-2">
            Share this link to earn referral bonuses
          </div>

          <div className="mt-4 bg-background p-3 rounded-lg border border-primary/20">
            <div className="text-xs text-white/70 mb-2">Referral Stats:</div>
            <div className="flex justify-between text-xs">
              <div>
                <div className="text-white/50">Direct Referrals:</div>
                <div className="text-white font-medium">{userData.directReferrals}</div>
              </div>
              <div>
                <div className="text-white/50">Earnings from Referrals:</div>
                <div className="text-highlight font-medium">${(userData.totalEarnings * 0.3).toFixed(2)}</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* QR Code Tab */}
      {activeTab === 'qr' && (
        <div className="text-center">
          <QRCode />
          <div className="mt-3 text-xs text-white/70">
            Scan this QR code to join using your mobile device
          </div>
          <button
            className="mt-3 bg-primary text-white text-xs py-1.5 px-3 rounded-lg hover:bg-primary/80 transition-colors"
            onClick={() => {
              // In a real app, this would generate and download a QR code image
              alert('QR Code download would start here');
            }}
          >
            Download QR Code
          </button>
        </div>
      )}

      {/* Social Tab */}
      {activeTab === 'social' && (
        <div>
          <div className="grid grid-cols-2 gap-3 mb-3">
            {socialOptions.map((option, index) => (
              <motion.a
                key={index}
                href={option.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center p-3 rounded-lg text-white"
                style={{ backgroundColor: option.color }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="mr-2">{option.icon}</span>
                <span className="text-xs font-medium">{option.name}</span>
              </motion.a>
            ))}
          </div>

          <div className="mt-3 bg-background p-3 rounded-lg border border-primary/20">
            <div className="text-xs text-white font-medium mb-1">Referral Message:</div>
            <div className="text-xs text-white/70 mb-2">
              Join me on Eternal Income Matrix and start earning passive income today!
            </div>
            <button
              className="text-xs text-primary hover:text-primary/80 transition-colors"
              onClick={() => {
                navigator.clipboard.writeText('Join me on Eternal Income Matrix and start earning passive income today! ' + referralLink);
                alert('Message copied to clipboard!');
              }}
            >
              Copy Message
            </button>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default ReferralLink;
