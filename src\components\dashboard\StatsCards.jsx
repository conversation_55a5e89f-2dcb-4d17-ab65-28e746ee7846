import React from 'react';
import { motion } from 'framer-motion';

const StatsCards = ({ userData }) => {
  // Define stats data for the new layout
  const statsData = [
    {
      title: 'Total Earnings',
      value: '$1,245.71',
      subtitle: 'Lifetime earnings',
      change: '+$2.50',
      color: '#FF0044',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    {
      title: 'Weekly Earnings',
      value: '$124.56',
      subtitle: 'Last 7 days',
      change: '+$2.25',
      color: '#FF0044',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
        </svg>
      )
    },
    {
      title: 'Partners',
      value: '16',
      subtitle: 'Active users',
      change: '+3',
      color: '#FF0044',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
      )
    },
    {
      title: 'Team Size',
      value: '42',
      subtitle: 'All levels combined',
      change: '+5',
      color: '#FF0044',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      )
    }
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4">
      {statsData.map((stat, index) => (
        <motion.div
          key={index}
          className="bg-background rounded-lg p-4 relative overflow-hidden border border-primary/20"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
          whileHover={{
            boxShadow: "0 0 15px rgba(255, 0, 68, 0.2)",
            borderColor: "rgba(255, 0, 68, 0.5)",
            y: -5
          }}
        >
          {/* Top icon */}
          <div className="absolute top-3 right-3 text-primary/70">
            {stat.icon}
          </div>

          {/* Glowing accent in corner */}
          <div
            className="absolute -top-10 -left-10 w-20 h-20 rounded-full blur-xl opacity-20"
            style={{ backgroundColor: stat.color }}
          ></div>

          <div className="flex flex-col">
            <div className="flex justify-between items-start mb-1">
              <span className="text-white/70 text-xs md:text-sm">{stat.title}</span>
            </div>

            <div className="flex items-baseline mt-2">
              <span className="text-white text-xl md:text-2xl font-bold">{stat.value}</span>
            </div>

            <div className="flex items-center justify-between mt-2">
              <div className="text-white/50 text-[10px] md:text-xs">
                {stat.subtitle}
              </div>

              <div className="flex items-center bg-highlight/10 px-1.5 py-0.5 rounded">
                <span className="text-highlight text-xs font-medium">{stat.change}</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 ml-0.5 text-highlight" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
              </div>
            </div>
          </div>

          {/* Bottom border accent */}
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-primary/50 to-transparent"></div>
        </motion.div>
      ))}
    </div>
  );
};

export default StatsCards;
