import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const TopBar = ({ userData }) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showCopyTooltip, setShowCopyTooltip] = useState(false);
  const [idHighlighted, setIdHighlighted] = useState(false);
  const [isEditingName, setIsEditingName] = useState(false);
  const [userName, setUserName] = useState(userData?.name || 'Eternal User');
  const fileInputRef = useRef(null);
  const [profileImage, setProfileImage] = useState(userData?.profileImage || null);

  // Add a subtle highlight effect to the ID when the component mounts
  useEffect(() => {
    // Initial highlight
    setIdHighlighted(true);

    // Turn off highlight after 2 seconds
    const timer = setTimeout(() => {
      setIdHighlighted(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const handleCopyLink = () => {
    navigator.clipboard.writeText(userData.referralLink);
    setShowCopyTooltip(true);
    setTimeout(() => setShowCopyTooltip(false), 2000);
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleNameChange = (e) => {
    setUserName(e.target.value);
  };

  const handleNameSave = () => {
    setIsEditingName(false);
    // Here you would typically save the changes to your backend
  };

  return (
    <div className="bg-[#0D0D0D] border-y-2 border-[#9F0000] py-4 px-4 md:px-8 flex flex-col md:flex-row items-start md:items-center justify-between relative z-20 shadow-[0_4px_6px_-1px_rgba(159,0,0,0.1)]">
      {/* Profile Section */}
      <div className="flex items-center w-full md:w-auto mb-4 md:mb-0 md:border-r border-[#9F0000]/30 md:pr-6">
        <div className="flex items-center">
          <div className="relative group">
            <div className="w-14 h-14 rounded-full bg-gradient-to-br from-[#1A1A1A] to-[#0D0D0D] flex items-center justify-center overflow-hidden border-2 border-[#9F0000] shadow-[0_0_15px_rgba(159,0,0,0.3)]">
              {/* Subtle glow effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-[#FF2C2C]/20 to-transparent opacity-50"></div>

              {profileImage ? (
                <img
                  src={profileImage}
                  alt="Profile"
                  className="w-full h-full object-cover relative z-10"
                />
              ) : (
                <span className="text-white font-bold text-2xl relative z-10">
                  {userName?.charAt(0) || 'E'}
                </span>
              )}

              {/* Edit overlay */}
              <div
                className="absolute inset-0 bg-black/60 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer z-20"
                onClick={() => fileInputRef.current.click()}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
            </div>

            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*"
              onChange={handleImageUpload}
            />

            {/* Level badge with gradient and glow */}
            <div className="absolute -bottom-1 -right-1 w-7 h-7 bg-gradient-to-br from-[#FF2C2C] to-[#9F0000] rounded-full flex items-center justify-center text-white text-xs font-bold border-2 border-[#0D0D0D] shadow-[0_0_10px_rgba(255,44,44,0.5)]">
              {userData?.level || 3}
            </div>
          </div>

          <div className="ml-4 relative">
            {/* Subtle background glow effect */}
            <div className="absolute -top-2 -left-2 w-20 h-20 bg-[#FF2C2C]/5 rounded-full blur-xl"></div>

            <div className="flex flex-col relative z-10">
              {/* User Name with Edit Option */}
              <div className="flex items-center mb-2">
                {isEditingName ? (
                  <div className="flex items-center">
                    <input
                      type="text"
                      value={userName}
                      onChange={handleNameChange}
                      className="bg-[#1A1A1A] text-white border border-[#9F0000]/50 rounded px-2 py-1 text-sm focus:outline-none focus:border-[#FF2C2C] w-full"
                      autoFocus
                      onBlur={handleNameSave}
                      onKeyPress={(e) => e.key === 'Enter' && handleNameSave()}
                    />
                    <button
                      className="ml-2 text-[#FF2C2C] hover:text-white"
                      onClick={handleNameSave}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center">
                    <span className="text-white font-bold text-lg">{userName}</span>
                    <button
                      className="ml-2 text-[#FFFFFF]/50 hover:text-[#FF2C2C] transition-colors"
                      onClick={() => setIsEditingName(true)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                      </svg>
                    </button>
                  </div>
                )}
              </div>

              {/* User ID */}
              <motion.div
                className="flex items-center bg-gradient-to-r from-[#1A1A1A] to-[#0D0D0D] px-4 py-2 rounded-md border-2 border-[#9F0000] shadow-[0_4px_6px_-1px_rgba(255,44,44,0.1)] relative"
                initial={{ borderColor: "#9F0000" }}
                animate={{
                  borderColor: idHighlighted ? "#FF2C2C" : "#9F0000",
                  boxShadow: idHighlighted ? "0 0 15px rgba(255,44,44,0.3)" : "0 4px 6px -1px rgba(255,44,44,0.1)"
                }}
                transition={{ duration: 0.5 }}
                whileHover={{
                  borderColor: "#FF2C2C",
                  boxShadow: "0 0 15px rgba(255,44,44,0.3)",
                  y: -2
                }}
              >
                {/* Border accents */}
                <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-[#9F0000] via-[#FF2C2C] to-[#9F0000]"></div>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#9F0000] via-[#FF2C2C] to-[#9F0000]"></div>
                <div className="absolute top-0 left-0 w-0.5 h-full bg-gradient-to-b from-[#9F0000] via-[#FF2C2C] to-[#9F0000]"></div>
                <div className="absolute top-0 right-0 w-0.5 h-full bg-gradient-to-b from-[#9F0000] via-[#FF2C2C] to-[#9F0000]"></div>

                <motion.span
                  className="text-[#FF2C2C] font-bold text-sm mr-1 relative z-10"
                  animate={{ opacity: idHighlighted ? [1, 0.7, 1] : 1 }}
                  transition={{ duration: 1.5, times: [0, 0.5, 1], repeat: idHighlighted ? 1 : 0 }}
                  style={{ textShadow: "0 0 5px rgba(255,44,44,0.3)" }}
                >
                  ID
                </motion.span>
                <motion.h2
                  className="text-2xl font-bold text-white tracking-wide relative z-10"
                  animate={{
                    textShadow: idHighlighted ? "0 0 8px rgba(255,44,44,0.5)" : "0 0 2px rgba(255,255,255,0.3)"
                  }}
                  transition={{ duration: 1 }}
                >
                  468
                </motion.h2>
                <div className="relative group ml-2">
                  <button
                    className="text-[#FFFFFF]/50 hover:text-[#FF2C2C] transition-colors"
                    onClick={() => {
                      navigator.clipboard.writeText("468");
                      setIdHighlighted(true);
                      setTimeout(() => setIdHighlighted(false), 1500);
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  </button>
                  <span className="absolute -top-8 left-1/2 -translate-x-1/2 bg-[#0D0D0D] border border-[#9F0000] text-white text-xs py-1 px-2 rounded opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap shadow-lg">
                    Copy ID
                  </span>
                </div>
              </motion.div>
            </div>

            <div className="text-sm text-[#FFFFFF] flex items-center relative z-10 mt-1">
              <div className="flex items-center bg-[#1A1A1A] rounded-full px-3 py-0.5 border-2 border-[#9F0000]/30">
                <span className="font-mono">0xb37e...0b68</span>
                <div className="relative group ml-2">
                  <button className="text-[#FFFFFF]/50 hover:text-[#FF2C2C] transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  </button>
                  <span className="absolute -top-8 left-1/2 -translate-x-1/2 bg-[#0D0D0D] border border-[#9F0000] text-white text-xs py-1 px-2 rounded opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap">
                    Copy Address
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center text-xs text-[#FFFFFF]/70 mt-2 relative z-10">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1 text-[#FF2C2C]/70" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              Invited 5/15/2023 by <span className="text-[#FF2C2C]/80 ml-1">ID 1</span>
            </div>
          </div>
        </div>
      </div>

      {/* Divider for mobile view */}
      <div className="w-full h-px bg-gradient-to-r from-transparent via-[#9F0000]/40 to-transparent my-4 md:hidden"></div>

      {/* Personal Link Section */}
      <div className="bg-[#1A1A1A] rounded-lg p-4 w-full md:w-auto border-2 border-[#9F0000]/40 relative overflow-hidden">
        {/* Subtle background glow effect */}
        <div className="absolute -bottom-10 -right-10 w-20 h-20 bg-[#FF2C2C]/5 rounded-full blur-xl"></div>

        <div className="flex items-center justify-between mb-2 relative z-10">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#FF2C2C] mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
            <span className="text-xs font-medium text-[#FFFFFF]">Personal Referral Link</span>
          </div>
          <div className="relative group">
            <button className="text-[#FF2C2C] hover:text-white transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
            <span className="absolute -top-8 right-0 bg-[#0D0D0D] border border-[#9F0000] text-white text-xs py-1 px-2 rounded opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap w-48">
              Share this link to earn referral bonuses
            </span>
          </div>
        </div>

        <div className="flex items-center relative z-10">
          <div className="bg-[#0D0D0D] rounded-md px-3 py-2 border-2 border-[#9F0000]/40 flex-1 flex items-center">
            <a href={userData?.referralLink} target="_blank" rel="noopener noreferrer" className="text-[#FF2C2C] font-medium text-sm hover:underline truncate">
              {userData?.referralLink || 'forsage.io/b/5lvio6'}
            </a>
            <button
              className="ml-2 text-[#FFFFFF]/50 hover:text-[#FF2C2C] transition-colors relative flex-shrink-0"
              onClick={handleCopyLink}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              {showCopyTooltip && (
                <span className="absolute -top-8 left-1/2 -translate-x-1/2 bg-[#0D0D0D] border border-[#9F0000] text-white text-xs py-1 px-2 rounded whitespace-nowrap shadow-lg">
                  Copied!
                </span>
              )}
            </button>
          </div>
        </div>

        <div className="flex items-center mt-3 relative z-10">
          <span className="text-xs text-[#FFFFFF]/70 mr-2">Not a member yet? Sign up with this upline</span>
          <button className="bg-[#FF2C2C] text-white text-xs py-1 px-3 rounded hover:bg-[#9F0000] transition-colors shadow-sm">
            Sign up
          </button>
        </div>
      </div>

      {/* Right side - Actions */}
      <div className="hidden md:flex items-center space-x-4 ml-4">
        {/* Balance */}
        <div className="bg-[#1A1A1A] rounded-lg px-3 py-1.5 border-2 border-[#9F0000]/40 relative overflow-hidden">
          <div className="absolute -top-10 -left-10 w-20 h-20 bg-[#FF2C2C]/5 rounded-full blur-xl"></div>
          <div className="text-white/50 text-[10px]">Balance</div>
          <div className="text-white text-sm font-medium">${userData?.balance?.toFixed(2) || '245.67'}</div>
        </div>

        {/* Notifications */}
        <div className="relative">
          <button
            className="p-2 rounded-full hover:bg-[#1A1A1A] transition-colors relative bg-[#0D0D0D] border-2 border-[#9F0000]/40"
            onClick={() => setShowNotifications(!showNotifications)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#FFFFFF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
            <span className="absolute top-1 right-1 w-2 h-2 bg-[#FF2C2C] rounded-full animate-pulse"></span>
          </button>

          {/* Notification Dropdown */}
          {showNotifications && (
            <motion.div
              className="absolute right-0 mt-2 w-80 bg-[#0D0D0D] border-2 border-[#9F0000]/40 rounded-lg shadow-lg overflow-hidden z-50"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2 }}
            >
              <div className="p-3 border-b border-[#9F0000]/20 flex justify-between items-center">
                <h3 className="text-white font-medium text-sm">Notifications</h3>
                <button className="text-white/50 hover:text-[#FF2C2C] text-xs">Mark all as read</button>
              </div>

              <div className="max-h-80 overflow-y-auto">
                {/* Notification Items */}
                <div className="p-3 border-b border-[#9F0000]/20 bg-[#FF2C2C]/5">
                  <div className="flex items-start">
                    <div className="w-2 h-2 rounded-full mt-1.5 mr-2 flex-shrink-0 bg-[#FF2C2C]"></div>
                    <div className="flex-1">
                      <p className="text-white text-xs">You received $42.56 from weekly distribution</p>
                      <p className="text-white/50 text-[10px] mt-1">2 hours ago</p>
                    </div>
                  </div>
                </div>

                <div className="p-3 border-b border-[#9F0000]/20 bg-[#FF2C2C]/5">
                  <div className="flex items-start">
                    <div className="w-2 h-2 rounded-full mt-1.5 mr-2 flex-shrink-0 bg-[#FF2C2C]"></div>
                    <div className="flex-1">
                      <p className="text-white text-xs">New referral joined your team</p>
                      <p className="text-white/50 text-[10px] mt-1">Yesterday</p>
                    </div>
                  </div>
                </div>

                <div className="p-3 border-b border-[#9F0000]/20">
                  <div className="flex items-start">
                    <div className="w-2 h-2 rounded-full mt-1.5 mr-2 flex-shrink-0 bg-white/30"></div>
                    <div className="flex-1">
                      <p className="text-white text-xs">Level 3 matrix slot filled</p>
                      <p className="text-white/50 text-[10px] mt-1">3 days ago</p>
                    </div>
                  </div>
                </div>

                <div className="p-3 border-b border-[#9F0000]/20">
                  <div className="flex items-start">
                    <div className="w-2 h-2 rounded-full mt-1.5 mr-2 flex-shrink-0 bg-white/30"></div>
                    <div className="flex-1">
                      <p className="text-white text-xs">Weekly distribution coming soon</p>
                      <p className="text-white/50 text-[10px] mt-1">5 days ago</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-2 border-t border-[#9F0000]/20 text-center">
                <button className="text-[#FF2C2C] hover:text-[#FF2C2C]/80 text-xs">View all notifications</button>
              </div>
            </motion.div>
          )}
        </div>

        {/* User Menu Button */}
        <div className="relative">
          <button
            className="p-2 rounded-full hover:bg-[#1A1A1A] transition-colors relative bg-[#0D0D0D] border-2 border-[#9F0000]/40"
            onClick={() => setShowUserMenu(!showUserMenu)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#FFFFFF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </button>

          {/* User Menu Dropdown */}
          {showUserMenu && (
            <motion.div
              className="absolute right-0 mt-2 w-48 bg-[#0D0D0D] border-2 border-[#9F0000]/40 rounded-lg shadow-lg overflow-hidden z-50"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2 }}
            >
              <div className="p-3 border-b border-[#9F0000]/20">
                <div className="text-white text-sm font-medium">{userName}</div>
                <div className="text-white/50 text-xs">ID: {userData?.id || '468'}</div>
              </div>

              <div className="py-1">
                <Link to="/dashboard/profile" className="flex items-center px-4 py-2 text-sm text-white/70 hover:bg-[#1A1A1A] hover:text-white">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-3 text-white/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Profile Settings
                </Link>

                <Link to="/dashboard/security" className="flex items-center px-4 py-2 text-sm text-white/70 hover:bg-[#1A1A1A] hover:text-white">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-3 text-white/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  Security
                </Link>

                <Link to="/dashboard/wallet" className="flex items-center px-4 py-2 text-sm text-white/70 hover:bg-[#1A1A1A] hover:text-white">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-3 text-white/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                  Wallet
                </Link>
              </div>

              <div className="py-1 border-t border-[#9F0000]/20">
                <Link to="/logout" className="flex items-center px-4 py-2 text-sm text-white/70 hover:bg-[#1A1A1A] hover:text-white">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-3 text-white/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  Sign Out
                </Link>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TopBar;
