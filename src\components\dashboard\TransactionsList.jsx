import React from 'react';
import { motion } from 'framer-motion';

const TransactionsList = ({ transactions }) => {
  return (
    <div className="w-full">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="text-left text-[#FFFFFF] text-sm">
              <th className="pb-3 font-medium">Type</th>
              <th className="pb-3 font-medium">Amount</th>
              <th className="pb-3 font-medium">Date</th>
              <th className="pb-3 font-medium">From</th>
            </tr>
          </thead>
          <tbody>
            {transactions?.map((tx) => (
              <tr key={tx.id} className="border-t border-[#9F0000]/20 hover:bg-[#1A1A1A] transition-colors duration-200">
                <td className="py-3">
                  <div className="flex items-center">
                    <div className={`w-2 h-2 rounded-full mr-2 ${
                      tx.type.includes('Referral') ? 'bg-[#00FFAA]' :
                      tx.type.includes('Weekly') ? 'bg-[#FF2C2C]' : 'bg-[#9F0000]'
                    }`}></div>
                    <span>{tx.type}</span>
                  </div>
                </td>
                <td className="py-3 font-medium">${tx.amount.toFixed(2)}</td>
                <td className="py-3 text-[#FFFFFF]/70">{tx.date}</td>
                <td className="py-3 text-[#FFFFFF]/70">{tx.from}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="mt-4 text-center">
        <a href="/dashboard/income" className="text-[#FF2C2C] hover:text-white transition-colors duration-300 relative group">
          <span>View All Transactions →</span>
          <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#FF2C2C]/70 group-hover:w-full transition-all duration-300"></span>
        </a>
      </div>
    </div>
  );
};

export default TransactionsList;
