import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';

const UserHeader = ({ userData }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [userName, setUserName] = useState(userData.name || 'Eternal User');
  const [profileImage, setProfileImage] = useState(userData.profileImage || null);
  const fileInputRef = useRef(null);

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleNameChange = (e) => {
    setUserName(e.target.value);
  };

  const handleSave = () => {
    setIsEditing(false);
    // Here you would typically save the changes to your backend
  };

  return (
    <motion.div
      className="bg-secondary rounded-lg p-4 md:p-5 border border-primary/20 relative overflow-hidden"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Background gradient effect */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary/0 via-primary to-primary/0"></div>
      <div className="absolute -top-24 -right-24 w-48 h-48 bg-primary/10 rounded-full blur-3xl"></div>

      <div className="flex flex-wrap md:flex-nowrap items-center relative z-10">
        {/* Profile Image and ID */}
        <div className="flex items-center w-full md:w-auto">
          <div className="relative group">
            <div className="w-16 h-16 md:w-20 md:h-20 bg-background rounded-lg flex items-center justify-center overflow-hidden border border-primary/50 shadow-lg">
              {profileImage ? (
                <img src={profileImage} alt="Profile" className="w-full h-full object-cover" />
              ) : (
                <span className="text-primary font-bold text-xl">
                  <svg width="45" height="45" viewBox="0 0 45 45" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22.5 0L33.75 11.25H11.25L22.5 0Z" fill="#FF0044"/>
                    <path d="M11.25 11.25V33.75L0 22.5L11.25 11.25Z" fill="#FF0044"/>
                    <path d="M33.75 11.25L45 22.5L33.75 33.75V11.25Z" fill="#FF0044"/>
                    <path d="M11.25 33.75H33.75L22.5 45L11.25 33.75Z" fill="#FF0044"/>
                  </svg>
                </span>
              )}

              {/* Edit overlay */}
              <div
                className="absolute inset-0 bg-black/60 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                onClick={() => fileInputRef.current.click()}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
            </div>
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*"
              onChange={handleImageUpload}
            />

            {/* Level badge */}
            <div className="absolute -bottom-2 -right-2 bg-primary text-white text-xs font-bold rounded-full w-7 h-7 flex items-center justify-center border-2 border-background shadow-lg">
              {userData.level}
            </div>
          </div>

          <div className="ml-4 md:ml-5">
            <div className="flex flex-col">
              {isEditing ? (
                <input
                  type="text"
                  value={userName}
                  onChange={handleNameChange}
                  className="bg-background text-white border border-primary/30 rounded px-2 py-1 text-sm md:text-base focus:outline-none focus:border-primary"
                  autoFocus
                  onBlur={handleSave}
                  onKeyPress={(e) => e.key === 'Enter' && handleSave()}
                />
              ) : (
                <div className="flex items-center">
                  <span className="text-white text-sm md:text-base font-medium">{userName}</span>
                  <button
                    className="ml-2 text-white/50 hover:text-primary transition-colors"
                    onClick={() => setIsEditing(true)}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                  </button>
                </div>
              )}

              <div className="text-xs md:text-sm text-white/50 font-medium mt-1">ID: {userData.id}</div>
              <div className="text-xs text-white/40">Joined: {new Date(userData.joinDate).toLocaleDateString()}</div>

              <div className="flex items-center mt-2">
                <div className="flex items-center">
                  <div className="h-2 w-2 rounded-full bg-green-500 mr-1"></div>
                  <span className="text-xs text-white/70">Active</span>
                </div>

                <div className="flex items-center ml-3 border-l border-white/10 pl-3">
                  <span className="text-xs text-white/70">Address:</span>
                  <span className="ml-1 text-xs text-primary">{userData.address}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Next Distribution */}
        <div className="ml-auto mt-4 md:mt-0 w-full md:w-auto">
          <motion.div
            className="bg-background rounded-lg p-3 border border-primary/20 shadow-md relative overflow-hidden"
            whileHover={{ boxShadow: "0 0 15px rgba(255, 0, 68, 0.15)" }}
          >
            <div className="absolute -top-10 -left-10 w-20 h-20 bg-primary/5 rounded-full blur-xl"></div>

            <div className="text-xs md:text-sm text-white/50 mb-1">Next Distribution</div>
            <div className="flex items-center justify-center">
              <div className="text-sm md:text-base text-white font-medium">6d</div>
              <div className="mx-1 text-xs text-white/50">:</div>
              <div className="text-sm md:text-base text-white font-medium">17h</div>
              <div className="mx-1 text-xs text-white/50">:</div>
              <div className="text-sm md:text-base text-white font-medium">9m</div>
            </div>
            <div className="text-[10px] md:text-xs text-white/50 mt-1 text-center">Ready to distribute</div>

            <div className="mt-2 pt-2 border-t border-white/10">
              <div className="flex justify-between items-center">
                <span className="text-[10px] text-white/50">Pool size:</span>
                <span className="text-xs text-highlight">$1,245.00</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default UserHeader;
