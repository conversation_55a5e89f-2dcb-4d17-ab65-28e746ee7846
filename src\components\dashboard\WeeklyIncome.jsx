import React, { useState } from 'react';
import { motion } from 'framer-motion';

const WeeklyIncome = ({ userData }) => {
  const [showHistory, setShowHistory] = useState(false);

  // Weekly distribution history
  const distributionHistory = [
    { date: '2023-08-06', amount: 42.56, poolSize: 1245.00, members: 28 },
    { date: '2023-07-30', amount: 38.75, poolSize: 1120.50, members: 26 },
    { date: '2023-07-23', amount: 35.20, poolSize: 980.75, members: 24 },
    { date: '2023-07-16', amount: 30.15, poolSize: 850.20, members: 22 }
  ];

  // Calculate percentage of pool
  const calculatePercentage = (amount, poolSize) => {
    return ((amount / poolSize) * 100).toFixed(1);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="grid grid-cols-2 gap-3 mb-4">
        <motion.div
          className="bg-background rounded-lg p-3 border border-primary/20 relative overflow-hidden"
          whileHover={{ boxShadow: "0 0 15px rgba(255, 0, 68, 0.15)" }}
        >
          <div className="absolute -top-10 -right-10 w-20 h-20 bg-primary/5 rounded-full blur-xl"></div>

          <div className="text-white/70 text-xs mb-1">Total Pool</div>
          <div className="text-white text-xl font-bold">$1,245.00</div>
          <div className="text-white/50 text-[10px] mt-1">Last week's total pool</div>

          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-primary/50 to-transparent"></div>
        </motion.div>

        <motion.div
          className="bg-background rounded-lg p-3 border border-primary/20 relative overflow-hidden"
          whileHover={{ boxShadow: "0 0 15px rgba(255, 0, 68, 0.15)" }}
        >
          <div className="absolute -top-10 -left-10 w-20 h-20 bg-highlight/5 rounded-full blur-xl"></div>

          <div className="text-white/70 text-xs mb-1">Your Share</div>
          <div className="text-white text-xl font-bold">$42.56</div>
          <div className="text-white/50 text-[10px] mt-1">
            <span className="text-highlight">{calculatePercentage(42.56, 1245.00)}%</span> of pool
          </div>

          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-highlight/50 to-transparent"></div>
        </motion.div>
      </div>

      <div className="flex justify-between items-center mb-3">
        <h3 className="text-white text-sm font-medium">Distribution Info</h3>
        <button
          className="text-xs text-primary hover:text-primary/80 transition-colors"
          onClick={() => setShowHistory(!showHistory)}
        >
          {showHistory ? 'Hide History' : 'Show History'}
        </button>
      </div>

      <motion.div
        className="bg-background rounded-lg p-3 border border-primary/20 mb-4 relative overflow-hidden"
        whileHover={{ boxShadow: "0 0 15px rgba(255, 0, 68, 0.15)" }}
      >
        <div className="flex justify-between">
          <div>
            <div className="text-white/70 text-xs mb-1">Active Members</div>
            <div className="text-white text-xl font-bold">28</div>
            <div className="text-white/50 text-[10px] mt-1">Ready for distribution</div>
          </div>

          <div>
            <div className="text-white/70 text-xs mb-1">Next Distribution</div>
            <div className="text-white text-base font-bold">Sunday</div>
            <div className="text-white/50 text-[10px] mt-1">In 6d 17h 9m</div>
          </div>
        </div>

        {/* Progress bar showing days until distribution */}
        <div className="mt-3 h-1.5 bg-background/50 rounded-full overflow-hidden">
          <motion.div
            className="h-full bg-primary"
            initial={{ width: 0 }}
            animate={{ width: '15%' }} // 1 out of 7 days passed
            transition={{ duration: 1, ease: "easeOut" }}
          />
        </div>

        <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-primary/50 to-transparent"></div>
      </motion.div>

      {/* Distribution History */}
      {showHistory && (
        <motion.div
          className="mb-4"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.3 }}
        >
          <div className="bg-background rounded-lg p-3 border border-primary/20">
            <h4 className="text-white text-xs font-medium mb-2">Distribution History</h4>
            <div className="space-y-2">
              {distributionHistory.map((item, index) => (
                <motion.div
                  key={index}
                  className="flex justify-between items-center text-xs border-b border-white/5 pb-2"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="text-white/70">{new Date(item.date).toLocaleDateString()}</div>
                  <div className="text-highlight font-medium">${item.amount}</div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      )}

      <div className="space-y-2">
        <h3 className="text-white text-sm font-medium mb-2">Distribution Rules</h3>

        <div className="bg-background rounded-lg p-3 border border-primary/20">
          <div className="space-y-3">
            <motion.div
              className="flex items-start"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
            >
              <div className="bg-primary/20 rounded-full p-1 mr-2 flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-primary" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="text-white/70 text-xs">5% from each new member fee is added to the weekly fund</div>
            </motion.div>

            <motion.div
              className="flex items-start"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className="bg-primary/20 rounded-full p-1 mr-2 flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-primary" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="text-white/70 text-xs">Distribution happens every Sunday automatically</div>
            </motion.div>

            <motion.div
              className="flex items-start"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              <div className="bg-primary/20 rounded-full p-1 mr-2 flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-primary" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="text-white/70 text-xs">Only active members (who referred at least 1 person that week) are eligible</div>
            </motion.div>

            <motion.div
              className="flex items-start"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
            >
              <div className="bg-primary/20 rounded-full p-1 mr-2 flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-primary" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="text-white/70 text-xs">Higher levels receive proportionally larger shares of the pool</div>
            </motion.div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default WeeklyIncome;
