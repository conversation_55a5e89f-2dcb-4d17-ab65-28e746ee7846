[{"inputs": [{"internalType": "address", "name": "_usdtToken", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "referrer", "type": "address"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "DirectReferralPaid", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "receiver", "type": "address"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "SpilloverPaid", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "oldLevel", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newLevel", "type": "uint256"}], "name": "UserLevelChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "week", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "membersCount", "type": "uint256"}], "name": "WeeklyFundDistributed", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "activeMembers", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentWeek", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "distributeWeeklyFund", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getTimeUntilNextDistribution", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "getUserLevel", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "getUserStats", "outputs": [{"internalType": "uint256", "name": "directReferrals", "type": "uint256"}, {"internalType": "uint256", "name": "totalTeamSize", "type": "uint256"}, {"internalType": "uint256", "name": "totalEarnings", "type": "uint256"}, {"internalType": "uint256", "name": "weeklyEarnings", "type": "uint256"}, {"internalType": "uint256", "name": "level", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "getUserTeam", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}, {"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_referrer", "type": "address"}], "name": "join", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "levelPercentages", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "levelThresholds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "markActive", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "matrices", "outputs": [], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nextDistributionTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "totalMembers", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_newUsdtToken", "type": "address"}], "name": "updateUsdtToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "usdtToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "users", "outputs": [{"internalType": "address", "name": "userAddress", "type": "address"}, {"internalType": "address", "name": "referrer", "type": "address"}, {"internalType": "uint256", "name": "directReferrals", "type": "uint256"}, {"internalType": "uint256", "name": "totalTeamSize", "type": "uint256"}, {"internalType": "uint256", "name": "spilloversReceived", "type": "uint256"}, {"internalType": "uint256", "name": "spilloversGenerated", "type": "uint256"}, {"internalType": "uint256", "name": "totalEarnings", "type": "uint256"}, {"internalType": "uint256", "name": "weeklyEarnings", "type": "uint256"}, {"internalType": "uint256", "name": "lastActiveTime", "type": "uint256"}, {"internalType": "bool", "name": "isActive", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "weeklyFund", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}]