// Mock user data for dashboard
export const mockUserData = {
  id: '468',
  name: 'Eternal User',
  address: '0xb37e...0b68',
  balance: 1245.71,
  level: 3,
  directReferrals: 16,
  totalTeam: 42,
  activeMembers: 28,
  weeklyIncome: 124.56,
  totalEarnings: 1245.71,
  profitRatio: 168,
  investmentTotal: 93.25,
  nextDistribution: 561600, // 6 days, 12 hours in seconds
  referralLink: 'https://eternal.matrix.io/ref/468',
  joinDate: '2023-05-15', // Format: YYYY-MM-DD
  // Optional profile image - uncomment to test with an image
  // profileImage: 'https://randomuser.me/api/portraits/men/32.jpg',
  directTeam: [
    {
      id: 1,
      address: '0x1234...5678',
      level: 2,
      directReferrals: 8,
      joinDate: '2023-06-01',
      isActive: true,
      isSpillover: false
    },
    {
      id: 2,
      address: '0xabcd...efgh',
      level: 1,
      directReferrals: 3,
      joinDate: '2023-06-15',
      isActive: true,
      isSpillover: false
    },
    {
      id: 3,
      address: '0x9876...5432',
      level: 2,
      directReferrals: 5,
      joinDate: '2023-07-01',
      isActive: true,
      isSpillover: true
    },
    {
      id: 4,
      address: '0xijkl...mnop',
      level: 1,
      directReferrals: 0,
      joinDate: '2023-07-15',
      isActive: false,
      isSpillover: true
    }
  ],
  recentTransactions: [
    {
      id: 1,
      type: 'Referral Bonus',
      amount: 12.5,
      date: '2023-08-01',
      from: '0x1234...5678'
    },
    {
      id: 2,
      type: 'Weekly Distribution',
      amount: 8.75,
      date: '2023-07-28',
      from: 'System'
    },
    {
      id: 3,
      type: 'Spillover Bonus',
      amount: 6.25,
      date: '2023-07-25',
      from: '0x9876...5432'
    },
    {
      id: 4,
      type: 'Referral Bonus',
      amount: 12.5,
      date: '2023-07-20',
      from: '0xabcd...efgh'
    },
    {
      id: 5,
      type: 'Weekly Distribution',
      amount: 8.75,
      date: '2023-07-21',
      from: 'System'
    }
  ],
  programs: [
    {
      id: 'x3',
      title: 'x3',
      price: '10 BUSD',
      missedProfits: '30 BUSD',
      color: '#FF2C2C',
      slots: [
        { filled: true, color: '#FF2C2C' },
        { filled: true, color: '#FF2C2C' },
        { filled: true, color: '#00FFAA' },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
      ]
    },
    {
      id: 'x4',
      title: 'x4',
      price: '20 BUSD',
      color: '#FF2C2C',
      slots: [
        { filled: true, color: '#FF2C2C' },
        { filled: true, color: '#00FFAA' },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
      ]
    },
    {
      id: 'x5',
      title: 'x5',
      price: '40 BUSD',
      color: '#9F0000',
      slots: [
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
      ]
    },
    {
      id: 'x6',
      title: 'x6',
      price: '80 BUSD',
      color: '#FF9900',
      slots: [
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
        { filled: false },
      ]
    }
  ]
};

export default mockUserData;
