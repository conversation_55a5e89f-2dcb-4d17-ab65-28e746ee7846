import { useState, useEffect, createContext, useContext, useCallback } from 'react';
import { ethers } from 'ethers';
import EternalIncomeMatrixABI from '../contracts/EternalIncomeMatrix.json';
import USDTTokenABI from '../contracts/USDTToken.json';

// Contract addresses (replace with actual deployed addresses)
const ETERNAL_CONTRACT_ADDRESS = '******************************************';
const USDT_CONTRACT_ADDRESS = '******************************************'; // USDT on BSC

// Create Web3 context
const Web3Context = createContext(null);

export function Web3Provider({ children }) {
  const [provider, setProvider] = useState(null);
  const [signer, setSigner] = useState(null);
  const [account, setAccount] = useState(null);
  const [chainId, setChainId] = useState(null);
  const [eternalContract, setEternalContract] = useState(null);
  const [usdtContract, setUsdtContract] = useState(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState(null);
  const [balance, setBalance] = useState({ bnb: '0', usdt: '0' });

  // Initialize provider
  useEffect(() => {
    if (window.ethereum) {
      const provider = new ethers.providers.Web3Provider(window.ethereum);
      setProvider(provider);
    }
  }, []);

  // Setup contracts when provider changes
  useEffect(() => {
    if (provider && signer) {
      try {
        const eternalContract = new ethers.Contract(
          ETERNAL_CONTRACT_ADDRESS,
          EternalIncomeMatrixABI,
          signer
        );
        setEternalContract(eternalContract);

        const usdtContract = new ethers.Contract(
          USDT_CONTRACT_ADDRESS,
          USDTTokenABI,
          signer
        );
        setUsdtContract(usdtContract);
      } catch (error) {
        console.error('Error setting up contracts:', error);
        setError('Failed to initialize contracts');
      }
    }
  }, [provider, signer]);

  // Update balances when account changes
  useEffect(() => {
    const updateBalances = async () => {
      if (provider && account) {
        try {
          // Get BNB balance
          const bnbBalance = await provider.getBalance(account);
          const formattedBnbBalance = ethers.utils.formatEther(bnbBalance);
          
          // Get USDT balance
          const usdtBalance = await usdtContract.balanceOf(account);
          const formattedUsdtBalance = ethers.utils.formatUnits(usdtBalance, 6); // USDT has 6 decimals on BSC
          
          setBalance({
            bnb: formattedBnbBalance,
            usdt: formattedUsdtBalance
          });
        } catch (error) {
          console.error('Error fetching balances:', error);
        }
      }
    };

    if (account && usdtContract) {
      updateBalances();
    }
  }, [provider, account, usdtContract]);

  // Connect wallet
  const connectWallet = useCallback(async (walletType) => {
    if (!provider) {
      setError('No provider available');
      return;
    }

    setIsConnecting(true);
    setError(null);

    try {
      // Request account access
      const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
      
      // Get network
      const network = await provider.getNetwork();
      setChainId(network.chainId);
      
      // Check if we're on BSC
      if (network.chainId !== 56) { // BSC Mainnet
        setError('Please connect to Binance Smart Chain');
        setIsConnecting(false);
        return;
      }
      
      // Set account and signer
      setAccount(accounts[0]);
      setSigner(provider.getSigner());
      
      setIsConnecting(false);
    } catch (error) {
      console.error('Error connecting wallet:', error);
      setError(error.message || 'Failed to connect wallet');
      setIsConnecting(false);
    }
  }, [provider]);

  // Disconnect wallet
  const disconnectWallet = useCallback(() => {
    setAccount(null);
    setSigner(null);
    setEternalContract(null);
    setUsdtContract(null);
    setBalance({ bnb: '0', usdt: '0' });
  }, []);

  // Join the Eternal Income Matrix
  const joinEternalMatrix = useCallback(async (referrerAddress) => {
    if (!eternalContract || !usdtContract || !account) {
      setError('Wallet not connected or contracts not initialized');
      return;
    }

    try {
      // First approve USDT transfer
      const joinFee = ethers.utils.parseUnits('6', 6); // 6 USDT with 6 decimals
      const approveTx = await usdtContract.approve(ETERNAL_CONTRACT_ADDRESS, joinFee);
      await approveTx.wait();
      
      // Then join the matrix
      const joinTx = await eternalContract.join(referrerAddress || ethers.constants.AddressZero);
      await joinTx.wait();
      
      return true;
    } catch (error) {
      console.error('Error joining matrix:', error);
      setError(error.message || 'Failed to join matrix');
      return false;
    }
  }, [eternalContract, usdtContract, account]);

  // Get user data
  const getUserData = useCallback(async (userAddress) => {
    if (!eternalContract) {
      setError('Contracts not initialized');
      return null;
    }

    try {
      const address = userAddress || account;
      if (!address) return null;
      
      // Get user stats
      const stats = await eternalContract.getUserStats(address);
      
      // Get user team
      const team = await eternalContract.getUserTeam(address);
      
      // Get time until next distribution
      const timeUntilDistribution = await eternalContract.getTimeUntilNextDistribution();
      
      return {
        directReferrals: stats.directReferrals.toNumber(),
        totalTeamSize: stats.totalTeamSize.toNumber(),
        totalEarnings: ethers.utils.formatUnits(stats.totalEarnings, 6),
        weeklyEarnings: ethers.utils.formatUnits(stats.weeklyEarnings, 6),
        level: stats.level.toNumber(),
        directTeam: team[0],
        spilloverTeam: team[1],
        timeUntilDistribution: timeUntilDistribution.toNumber()
      };
    } catch (error) {
      console.error('Error fetching user data:', error);
      setError(error.message || 'Failed to fetch user data');
      return null;
    }
  }, [eternalContract, account]);

  // Listen for account changes
  useEffect(() => {
    if (window.ethereum) {
      const handleAccountsChanged = (accounts) => {
        if (accounts.length === 0) {
          // User disconnected
          disconnectWallet();
        } else if (accounts[0] !== account) {
          // Account changed
          setAccount(accounts[0]);
          setSigner(provider?.getSigner());
        }
      };

      const handleChainChanged = (chainId) => {
        // Chain changed, reload the page
        window.location.reload();
      };

      window.ethereum.on('accountsChanged', handleAccountsChanged);
      window.ethereum.on('chainChanged', handleChainChanged);

      return () => {
        window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
        window.ethereum.removeListener('chainChanged', handleChainChanged);
      };
    }
  }, [account, disconnectWallet, provider]);

  // Context value
  const value = {
    provider,
    signer,
    account,
    chainId,
    eternalContract,
    usdtContract,
    isConnecting,
    error,
    balance,
    connectWallet,
    disconnectWallet,
    joinEternalMatrix,
    getUserData
  };

  return <Web3Context.Provider value={value}>{children}</Web3Context.Provider>;
}

// Hook to use the Web3 context
export function useWeb3() {
  const context = useContext(Web3Context);
  if (!context) {
    throw new Error('useWeb3 must be used within a Web3Provider');
  }
  return context;
}

// Export a function to get a referral link
export function getReferralLink(address) {
  if (!address) return '';
  return `${window.location.origin}?ref=${address}`;
}

// Export a function to get a referrer from URL
export function getReferrerFromURL() {
  const params = new URLSearchParams(window.location.search);
  return params.get('ref') || '';
}
