@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* Tailwind directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-color: #9F0000;       /* Dark Red */
  --secondary-color: #3A3A3A;     /* Dark Grey */
  --background-color: #000000;    /* Pure Black */
  --accent-color: #FF2C2C;        /* Electric Red Highlights */
  --contrast-color: #BFBFBF;      /* Metallic Grey for contrast */
  --highlight-color: #FF2C2C;     /* Electric Red for highlights */
  --dark-color: #1A1A1A;          /* Slightly lighter black */
  --card-color: #1A1A1A;          /* Dark grey for cards */
}

body {
  margin: 0;
  font-family: 'Rajdhani', sans-serif;
  background-color: var(--background-color);
  color: var(--contrast-color);
  background-image: radial-gradient(circle at 50% 50%, var(--dark-color) 0%, var(--background-color) 100%);
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Orbitron', sans-serif;
}

/* Component styles */
@layer components {
  .btn-primary {
    /* Tailwind classes */
    @apply bg-primary text-white font-orbitron font-bold py-3 px-6 rounded-md hover:bg-accent transition-all duration-300 shadow-lg shadow-primary/30 border border-primary/50;
    /* Custom properties */
    background-color: var(--primary-color);
    border-color: rgba(159, 0, 0, 0.5);
    box-shadow: 0 0 15px rgba(159, 0, 0, 0.3);
  }

  .btn-primary:hover {
    background-color: var(--accent-color);
    border-color: rgba(255, 44, 44, 0.5);
    box-shadow: 0 0 20px rgba(255, 44, 44, 0.4);
  }

  .neon-glow {
    position: relative;
    text-shadow: 0 0 10px var(--accent-color), 0 0 20px var(--accent-color);
  }

  .neon-text-purple {
    position: relative;
    text-shadow: 0 0 10px var(--primary-color), 0 0 20px var(--primary-color), 0 0 30px var(--highlight-color);
    animation: pulse-purple 2s infinite alternate;
  }

  .neon-text-red {
    position: relative;
    text-shadow: 0 0 10px var(--accent-color), 0 0 20px var(--accent-color);
    animation: pulse-red 2s infinite alternate;
  }

  @keyframes pulse-purple {
    0% {
      text-shadow: 0 0 10px var(--primary-color), 0 0 20px var(--primary-color);
    }
    100% {
      text-shadow: 0 0 15px var(--primary-color), 0 0 25px var(--primary-color), 0 0 35px var(--highlight-color);
    }
  }

  .shadow-glow {
    box-shadow: 0 0 15px rgba(159, 0, 0, 0.4);
  }

  .neon-border {
    position: relative;
    border: 2px solid var(--accent-color);
    box-shadow: 0 0 10px var(--accent-color), inset 0 0 10px var(--accent-color);
  }

  .matrix-grid {
    display: grid;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    gap: 1rem;
  }

  /* Gaming style scrollbar */
  ::-webkit-scrollbar {
    width: 10px;
    background-color: var(--dark-color);
  }

  ::-webkit-scrollbar-thumb {
    background-color: var(--primary-color);
    border-radius: 5px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: var(--accent-color);
  }

  /* Hide scrollbar class */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  /* Dashboard container */
  .dashboard-container {
    max-width: 100%;
    overflow-x: hidden;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .dashboard-content {
    width: 100%;
    max-width: 1200px;
    overflow-x: hidden;
  }

  /* Prevent horizontal scrolling globally */
  html, body, #root, .App {
    max-width: 100%;
    overflow-x: hidden;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) #0a0a0a;
  }

  /* Fix dashboard horizontal scrolling */
  .dashboard-content,
  .overflow-x-auto {
    max-width: 100%;
  }

  /* Fix card styling */
  .card-shadow {
    box-shadow: 0 0 15px rgba(159, 0, 0, 0.2) !important;
    border: 1px solid rgba(159, 0, 0, 0.25) !important;
  }

  /* Override all card styles */
  .bg-\[\#1A1A1A\].text-white.rounded-2xl.p-6,
  .bg-card.text-white.rounded-2xl.p-6 {
    box-shadow: 0 0 15px rgba(159, 0, 0, 0.2) !important;
    border: 1px solid rgba(159, 0, 0, 0.25) !important;
    background-color: var(--card-color) !important;
  }

  .shadow-\[0_0_15px_rgba\(159\,0\,0\,0\.2\)\] {
    box-shadow: 0 0 15px rgba(159, 0, 0, 0.2) !important;
  }

  .border-\[\#9F0000\]\/50, .border-\[\#9F0000\]\/30, .border-\[\#9F0000\],
  .border-primary\/50, .border-primary\/30, .border-primary\/20, .border-primary {
    border-color: rgba(159, 0, 0, 0.25) !important;
  }

  /* Update horizontal lines */
  .border-t.border-primary\/10,
  .border-t.border-primary\/20,
  .border-t.border-primary\/30,
  .border-t.border-\[\#9F0000\]\/10,
  .border-t.border-\[\#9F0000\]\/20,
  .border-t.border-\[\#9F0000\]\/30,
  .border-t.border-accent,
  .border-t.border-accent\/10,
  .border-t.border-accent\/20,
  .border-t.border-accent\/30 {
    border-top-color: rgba(159, 0, 0, 0.15) !important;
  }

  /* Override horizontal dividers */
  hr, .border-t {
    border-top-color: rgba(159, 0, 0, 0.15) !important;
  }

  /* Fix progress indicators */
  .mt-4.pt-4.border-t.border-primary\/10 {
    border-top-color: rgba(159, 0, 0, 0.15) !important;
  }

  /* Update progress bars */
  .bg-primary {
    background-color: var(--primary-color) !important;
  }

  .from-primary\/80, .to-primary\/40 {
    background-image: linear-gradient(to right, rgba(159, 0, 0, 0.8), rgba(255, 44, 44, 0.6)) !important;
  }

  /* Update text colors */
  .text-accent {
    color: var(--accent-color) !important;
  }

  .text-primary {
    color: var(--primary-color) !important;
  }

  .text-\[\#FF2C2C\] {
    color: var(--accent-color) !important;
  }

  /* Update SVG icons */
  svg.text-accent {
    color: var(--accent-color) !important;
  }

  svg.text-primary {
    color: var(--primary-color) !important;
  }

  svg.text-\[\#FF2C2C\],
  .h-5.w-5.mr-2.text-accent.flex-shrink-0 {
    color: var(--accent-color) !important;
  }

  /* Update section headings */
  .bg-\[\#1A1A1A\].text-white.rounded-2xl.p-6 h2.text-xl.font-orbitron.font-bold.text-accent.mb-4,
  .bg-card.text-white.rounded-2xl.p-6 h2.text-xl.font-orbitron.font-bold.text-accent.mb-4 {
    color: var(--accent-color) !important;
    border-bottom: none !important;
  }

  /* Fix Weekly Income Distribution section */
  h2.text-xl.font-orbitron.font-bold.text-accent.mb-4 + div.grid.grid-cols-1.md\:grid-cols-3.gap-6.mb-6 {
    border-top: none !important;
  }

  /* Fix Level Progress section */
  .bg-primary.h-4.rounded-full.relative {
    background-color: #333333 !important;
  }

  .bg-primary.h-4.rounded-full.relative .bg-gradient-to-r.from-primary\/80.to-primary {
    background-image: linear-gradient(to right, #333333, #444444) !important;
  }

  /* Fix red text in Level Progress */
  .text-accent.font-medium {
    color: #BFBFBF !important;
  }

  /* Update all borders */
  hr,
  .border-b,
  .border-t,
  .border-l,
  .border-r,
  [class*="border-[#9F0000]"],
  [class*="border-[#FF2C2C]"] {
    border-color: rgba(159, 0, 0, 0.25) !important;
  }

  /* Update card borders */
  .card-shadow,
  .border.border-\[\#9F0000\]\/20,
  .border.border-\[\#9F0000\]\/20\/20,
  .border.border-\[\#9F0000\]\/20\/30,
  .border.border-primary\/20,
  .border.border-secondary\/30,
  .border.border-secondary\/20 {
    border-color: rgba(159, 0, 0, 0.25) !important;
    box-shadow: 0 0 15px rgba(159, 0, 0, 0.2) !important;
  }

  /* Update section borders */
  .bg-\[\#1A1A1A\] h2 + div,
  .bg-\[\#1A1A1A\] h3 + div,
  .bg-card h2 + div,
  .bg-card h3 + div {
    border-top-color: rgba(159, 0, 0, 0.15) !important;
  }

  /* Update card backgrounds */
  .bg-\[\#1A1A1A\] {
    background-color: var(--card-color) !important;
  }

  .bg-background\/30, .bg-background\/20, .bg-background\/50 {
    background-color: rgba(10, 10, 20, 0.3) !important;
  }

  /* Fix growth rate line */
  .text-xs.text-contrast\/60.mb-1 + div.w-full.bg-background\/50.rounded-full.h-1\.5.overflow-hidden {
    border-top: none !important;
  }

  /* Update glowing text */
  .text-\[\#FF2C2C\].neon-glow {
    color: var(--accent-color) !important;
    text-shadow: 0 0 10px var(--accent-color), 0 0 20px var(--accent-color) !important;
  }

  /* Update Next Distribution section */
  .bg-gradient-to-r.from-\[\#9F0000\]\/20.to-\[\#FF2C2C\]\/10 {
    background-image: linear-gradient(to right, rgba(159, 0, 0, 0.2), rgba(255, 44, 44, 0.1)) !important;
  }

  /* Update table headers */
  th.py-3.px-4.text-accent {
    color: var(--accent-color) !important;
  }

  /* Update card background effects */
  .bg-primary\/10 {
    background-color: rgba(159, 0, 0, 0.1) !important;
  }

  .bg-primary\/20 {
    background-color: rgba(159, 0, 0, 0.2) !important;
  }

  .bg-primary\/5 {
    background-color: rgba(159, 0, 0, 0.05) !important;
  }

  .from-primary\/5 {
    background-image: linear-gradient(to bottom right, rgba(159, 0, 0, 0.05), transparent) !important;
  }

  .from-primary\/30 {
    background-image: linear-gradient(to right, rgba(159, 0, 0, 0.3), rgba(255, 44, 44, 0.2)) !important;
  }

  .to-primary\/50 {
    background-image: linear-gradient(to right, rgba(159, 0, 0, 0.3), rgba(255, 44, 44, 0.5)) !important;
  }

  /* Update button backgrounds */
  .bg-primary\/20.text-accent {
    background-color: rgba(159, 0, 0, 0.2) !important;
    color: var(--accent-color) !important;
  }

  .hover\:bg-primary\/30 {
    background-color: rgba(159, 0, 0, 0.2) !important;
  }

  .hover\:bg-primary\/30:hover {
    background-color: rgba(159, 0, 0, 0.3) !important;
  }

  /* Update tab buttons */
  .px-4.py-2.rounded-l-lg.text-sm.font-medium,
  .px-4.py-2.rounded-r-lg.text-sm.font-medium {
    background-color: rgba(20, 20, 31, 0.5) !important;
    color: var(--contrast-color) !important;
  }

  .px-4.py-2.rounded-l-lg.text-sm.font-medium.bg-primary.text-white,
  .px-4.py-2.rounded-r-lg.text-sm.font-medium.bg-primary.text-white {
    background-color: var(--primary-color) !important;
    color: white !important;
  }

  /* Update Join Again button */
  .bg-primary.text-white.font-orbitron.font-bold.rounded-md {
    background-color: var(--primary-color) !important;
    border-color: rgba(159, 0, 0, 0.5) !important;
  }

  .hover\:bg-primary\/80:hover {
    background-color: rgba(159, 0, 0, 0.8) !important;
  }

  /* Update gradient backgrounds */
  .bg-gradient-to-r.from-primary\/30.to-primary\/50 {
    background-image: linear-gradient(to right, rgba(159, 0, 0, 0.3), rgba(255, 44, 44, 0.5)) !important;
  }

  /* Logo styling */
  .logo-container {
    position: relative;
    display: inline-block;
  }

  .logo-image {
    filter: drop-shadow(0 0 10px rgba(159, 0, 0, 0.5));
    transition: filter 0.3s ease;
    background: transparent;
  }

  .logo-image:hover {
    filter: drop-shadow(0 0 15px rgba(159, 0, 0, 0.8));
  }

  /* Logo overlay removed */

  /* Update card hover effects */
  .motion-card-hover:hover {
    box-shadow: 0 0 25px rgba(159, 0, 0, 0.4) !important;
    transform: translateY(-5px);
  }

  /* Override all hover effects */
  [class*="whileHover"] {
    box-shadow: 0 0 25px rgba(159, 0, 0, 0.4) !important;
  }

  /* Standardize container widths */
  .site-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  /* Animated background for gaming effect */
  .bg-animated {
    background: linear-gradient(45deg, #0a0a0a, #1a1a1a);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }
}

/* Animations */
@keyframes pulse-red {
  0% {
    text-shadow: 0 0 10px rgba(255, 44, 44, 0.7), 0 0 20px rgba(255, 44, 44, 0.5);
  }
  100% {
    text-shadow: 0 0 15px rgba(255, 44, 44, 1), 0 0 30px rgba(255, 44, 44, 0.8), 0 0 45px rgba(255, 44, 44, 0.6);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
