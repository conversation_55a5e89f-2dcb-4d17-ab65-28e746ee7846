import { motion } from 'framer-motion'

const About = () => {
  return (
    <div className="py-16 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-orbitron font-bold text-primary mb-6">About ETERNAL</h1>
          <p className="text-xl text-contrast/80 max-w-3xl mx-auto">
            The revolutionary income matrix system with recursive spillover technology.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center mb-20">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-6">
              Our Vision
            </h2>
            <p className="text-contrast/80 mb-4">
              ETERNAL Income Matrix System was created with a simple vision: to provide a fair, transparent, and sustainable income opportunity for everyone, regardless of their background or experience.
            </p>
            <p className="text-contrast/80 mb-4">
              We believe that financial freedom should be accessible to all, and our innovative matrix system makes this possible through the power of community and smart contract technology.
            </p>
            <p className="text-contrast/80">
              By combining the best elements of traditional matrix plans with cutting-edge blockchain technology, we've created a system that is both powerful and easy to understand.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="bg-secondary/20 backdrop-blur-sm border border-secondary/30 rounded-lg p-8 neon-border"
          >
            <h3 className="text-xl font-orbitron font-bold text-white mb-4">ETERNAL Income Matrix System</h3>
            <p className="text-contrast/90 mb-4">
              Our system is an innovative 4×2 recursive spillover matrix plan that creates weekly income opportunities for members.
            </p>
            <p className="text-contrast/90 mb-4">
              Join with just $6 and start your income journey in our smart contract-based system.
            </p>
            <p className="text-contrast/90">
              Our recursive spillover technology helps you maximize benefits from your team's work, where your income increases with each new member joining the system.
            </p>
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mb-20"
        >
          <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-6 text-center">
            What Makes ETERNAL Different
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-background border border-secondary/30 rounded-lg p-6">
              <div className="text-primary text-4xl mb-4">01</div>
              <h3 className="text-xl font-orbitron font-bold text-white mb-2">Recursive Spillover</h3>
              <p className="text-contrast/80">
                Our unique recursive spillover system ensures that members benefit from the growth of their entire team, not just their direct referrals.
              </p>
            </div>

            <div className="bg-background border border-secondary/30 rounded-lg p-6">
              <div className="text-primary text-4xl mb-4">02</div>
              <h3 className="text-xl font-orbitron font-bold text-white mb-2">Weekly Income Fund</h3>
              <p className="text-contrast/80">
                The Eternal Fund distributes income to active members every week, providing a consistent source of passive income.
              </p>
            </div>

            <div className="bg-background border border-secondary/30 rounded-lg p-6">
              <div className="text-primary text-4xl mb-4">03</div>
              <h3 className="text-xl font-orbitron font-bold text-white mb-2">Level-Based Rewards</h3>
              <p className="text-contrast/80">
                As you grow your team and advance through levels, your share of the weekly fund increases, incentivizing continuous growth.
              </p>
            </div>

            <div className="bg-background border border-secondary/30 rounded-lg p-6">
              <div className="text-primary text-4xl mb-4">04</div>
              <h3 className="text-xl font-orbitron font-bold text-white mb-2">Blockchain Secured</h3>
              <p className="text-contrast/80">
                All transactions are secured by blockchain technology, ensuring transparency and immutability of records.
              </p>
            </div>

            <div className="bg-background border border-secondary/30 rounded-lg p-6">
              <div className="text-primary text-4xl mb-4">05</div>
              <h3 className="text-xl font-orbitron font-bold text-white mb-2">Low Entry Barrier</h3>
              <p className="text-contrast/80">
                With just $6 to join, ETERNAL is accessible to everyone, regardless of their financial situation.
              </p>
            </div>

            <div className="bg-background border border-secondary/30 rounded-lg p-6">
              <div className="text-primary text-4xl mb-4">06</div>
              <h3 className="text-xl font-orbitron font-bold text-white mb-2">Sustainable Model</h3>
              <p className="text-contrast/80">
                Our system is designed for long-term sustainability, with a balanced distribution of funds that benefits all members.
              </p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.8 }}
          className="bg-primary/10 backdrop-blur-sm border border-primary/30 rounded-lg p-8 text-center"
        >
          <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-white mb-6">
            Join the ETERNAL Community
          </h2>
          <p className="text-xl text-contrast/90 max-w-3xl mx-auto mb-8">
            Be part of a growing community of like-minded individuals who are building passive income streams together.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <a href="#" className="btn-primary">
              Telegram Community
            </a>
            <a href="#" className="bg-secondary text-white font-orbitron font-bold py-2 px-6 rounded-md hover:bg-secondary/80 transition-all duration-300 shadow-lg">
              Discord Server
            </a>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default About
