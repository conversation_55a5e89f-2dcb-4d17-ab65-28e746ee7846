import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'
import { useWeb3, getReferrerFromURL } from '../hooks/useWeb3.jsx'

// Import wallet icons
import metamaskIcon from '../assets/images/metamask.svg'
import trustwalletIcon from '../assets/images/trustwallet.svg'
import safepalIcon from '../assets/images/safepal.svg'
import tokenpocketIcon from '../assets/images/tokenpocket.svg'

const ConnectWallet = () => {
  const { account, balance, connectWallet: connectWeb3Wallet, joinEternalMatrix, isConnecting, error } = useWeb3()
  const [isConnected, setIsConnected] = useState(false)
  const [walletAddress, setWalletAddress] = useState('')
  const [walletBalance, setWalletBalance] = useState(null)
  const [referrerAddress, setReferrerAddress] = useState('')
  const [isApproved, setIsApproved] = useState(false)
  const [isJoining, setIsJoining] = useState(false)
  const [joinSuccess, setJoinSuccess] = useState(false)
  const [selectedWallet, setSelectedWallet] = useState(null)

  // Get referrer from URL if available
  useEffect(() => {
    const referrer = getReferrerFromURL()
    if (referrer) {
      setReferrerAddress(referrer)
    }
  }, [])

  // Wallet options
  const wallets = [
    {
      name: 'MetaMask',
      icon: metamaskIcon,
      description: 'Connect to your MetaMask Wallet',
    },
    {
      name: 'Trust Wallet',
      icon: trustwalletIcon,
      description: 'Connect to your Trust Wallet',
    },
    {
      name: 'SafePal',
      icon: safepalIcon,
      description: 'Connect to your SafePal Wallet',
    },
    {
      name: 'TokenPocket',
      icon: tokenpocketIcon,
      description: 'Connect to your TokenPocket Wallet',
    },
  ]

  // Connect wallet using Web3
  const connectWallet = async (wallet) => {
    setSelectedWallet(wallet)

    try {
      // Connect to wallet using Web3
      await connectWeb3Wallet(wallet.name.toLowerCase())

      // Update state with account and balance from Web3
      if (account) {
        setWalletAddress(account)
        setWalletBalance(balance)
        setIsConnected(true)
      }
    } catch (err) {
      console.error('Error connecting wallet:', err)
    }
  }

  // Update state when account or balance changes
  useEffect(() => {
    if (account) {
      setWalletAddress(account)
      setWalletBalance(balance)
      setIsConnected(true)
    } else {
      setIsConnected(false)
    }
  }, [account, balance])

  // Approve USDT and join the system
  const approveUSDT = async () => {
    // In a real implementation with Web3, the approval is handled in the joinEternalMatrix function
    setIsApproved(true)
  }

  // Join the ETERNAL system
  const joinSystem = async () => {
    setIsJoining(true)

    try {
      // Join the ETERNAL system using Web3
      const success = await joinEternalMatrix(referrerAddress || null)

      if (success) {
        setJoinSuccess(true)

        // Automatically redirect to dashboard after 3 seconds
        setTimeout(() => {
          window.location.href = '/dashboard'
        }, 3000)
      }
    } catch (err) {
      console.error('Error joining ETERNAL:', err)
    } finally {
      setIsJoining(false)
    }
  }

  // Disconnect wallet
  const disconnectWallet = () => {
    // In a real implementation, this would disconnect from the Web3 provider
    setIsConnected(false)
    setWalletAddress('')
    setWalletBalance(null)
    setIsApproved(false)
    setJoinSuccess(false)
    setSelectedWallet(null)

    // Redirect to home page
    window.location.href = '/'
  }

  return (
    <div className="pt-24 pb-16 bg-background relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background"></div>
        <div className="absolute inset-0 opacity-5"></div>
      </div>

      {/* Animated particles */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-primary rounded-full shadow-glow"
            initial={{
              x: Math.random() * 100 + "%",
              y: Math.random() * 100 + "%",
              opacity: Math.random() * 0.5 + 0.3
            }}
            animate={{
              y: [null, Math.random() * 100 + "%"],
              opacity: [null, Math.random() * 0.3 + 0.1]
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
        ))}
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <motion.div
            className="inline-block relative mb-6"
            animate={{
              scale: [1, 1.05, 1],
              rotate: [0, 1, 0, -1, 0]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          >
            <div className="absolute -inset-6 rounded-full bg-primary/20 blur-xl opacity-70"></div>
            <h1 className="text-4xl md:text-6xl font-orbitron font-bold text-primary neon-text-red relative">
              Connect Wallet
            </h1>
          </motion.div>

          <motion.p
            className="text-xl text-contrast/80 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            Connect your wallet to join the <span className="text-accent font-medium">ETERNAL</span> Income Matrix System
          </motion.p>
        </motion.div>

        {!isConnected ? (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mb-20"
          >
            <div className="bg-[#1A1A1A]/90 backdrop-blur-md border border-primary/30 rounded-2xl p-8 shadow-glow relative overflow-hidden">
              {/* Decorative elements */}
              <div className="absolute top-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-primary to-transparent"></div>
              <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-primary to-transparent"></div>
              <div className="absolute top-0 left-0 w-[1px] h-full bg-gradient-to-b from-transparent via-primary/20 to-transparent"></div>
              <div className="absolute top-0 right-0 w-[1px] h-full bg-gradient-to-b from-transparent via-primary/20 to-transparent"></div>

              <motion.h2
                className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-8 text-center relative"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-red-500 to-red-700">
                  Select Your Wallet
                </span>
              </motion.h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {wallets.map((wallet, index) => (
                  <motion.div
                    key={wallet.name}
                    className="bg-background/40 border border-secondary/30 rounded-xl p-6 cursor-pointer hover:border-primary/50 transition-all duration-300 relative group overflow-hidden"
                    whileHover={{
                      scale: 1.05,
                      boxShadow: "0 0 20px rgba(159, 0, 0, 0.3)"
                    }}
                    whileTap={{ scale: 0.98 }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{
                      duration: 0.5,
                      delay: 0.1 * index,
                      type: "spring",
                      stiffness: 300
                    }}
                    onClick={() => connectWallet(wallet)}
                  >
                    {/* Hover effect */}
                    <div className="absolute inset-0 bg-gradient-to-b from-primary/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    <div className="flex flex-col items-center text-center relative z-10">
                      <motion.div
                        className="w-20 h-20 mb-4 flex items-center justify-center bg-primary/10 rounded-full relative"
                        whileHover={{ rotate: 10 }}
                      >
                        <div className="absolute inset-0 rounded-full bg-primary/5 blur-md"></div>
                        <img src={wallet.icon} alt={wallet.name} className="w-12 h-12 relative z-10" />
                      </motion.div>

                      <h3 className="text-xl font-orbitron font-bold text-white mb-2">{wallet.name}</h3>
                      <p className="text-contrast/80 text-sm">{wallet.description}</p>

                      <motion.div
                        className="mt-4 text-accent text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        initial={{ y: 10 }}
                        whileHover={{ y: 0 }}
                      >
                        Connect Now →
                      </motion.div>
                    </div>
                  </motion.div>
                ))}
              </div>

              <motion.div
                className="mt-10 text-center text-contrast/70 text-sm bg-background/30 p-4 rounded-lg border border-secondary/20"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.8 }}
              >
                <p>Don't have a wallet yet? Download one of the wallets above to get started.</p>
                <p className="mt-2">Make sure your wallet is configured for <span className="text-accent">Binance Smart Chain (BSC)</span>.</p>
              </motion.div>
            </div>
          </motion.div>
        ) : joinSuccess ? (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-20"
          >
            <div className="bg-[#1A1A1A]/90 backdrop-blur-md border border-primary/30 rounded-2xl p-10 shadow-glow relative overflow-hidden text-center">
              {/* Decorative elements */}
              <div className="absolute top-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-primary to-transparent"></div>
              <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-primary to-transparent"></div>
              <div className="absolute top-0 left-0 w-[1px] h-full bg-gradient-to-b from-transparent via-primary/20 to-transparent"></div>
              <div className="absolute top-0 right-0 w-[1px] h-full bg-gradient-to-b from-transparent via-primary/20 to-transparent"></div>

              {/* Success animation */}
              <motion.div
                className="relative mb-8"
                initial={{ scale: 0, rotate: 0 }}
                animate={{ scale: 1, rotate: [0, 15, -15, 0] }}
                transition={{
                  duration: 1.2,
                  type: "spring",
                  bounce: 0.5
                }}
              >
                <div className="absolute inset-0 rounded-full bg-green-500/20 blur-xl"></div>
                <div className="text-green-500 text-6xl relative">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-28 w-28 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.5 }}
              >
                <h2 className="text-3xl md:text-4xl font-orbitron font-bold text-accent mb-6">
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-red-500 to-red-700">
                    Welcome to ETERNAL!
                  </span>
                </h2>

                <p className="text-xl text-contrast/90 mb-4">
                  You have successfully joined the <span className="text-accent font-medium">ETERNAL</span> Income Matrix System.
                </p>
                <p className="text-contrast/80 mb-10">
                  <span className="text-green-400">Redirecting to Dashboard in 3 seconds...</span>
                </p>

                <div className="flex flex-col sm:flex-row justify-center gap-6">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="relative"
                  >
                    <div className="absolute -inset-1 bg-gradient-to-r from-primary/30 to-primary/50 rounded-md blur-sm"></div>
                    <Link
                      to="/dashboard"
                      className="relative px-8 py-4 bg-primary text-white font-orbitron font-bold rounded-md hover:bg-primary/80 transition-all duration-300 shadow-glow border border-primary/50 flex items-center justify-center"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      Go to Dashboard
                    </Link>
                  </motion.div>
                </div>
              </motion.div>

              {/* Confetti animation */}
              <div className="absolute inset-0 z-0 overflow-hidden pointer-events-none">
                {[...Array(30)].map((_, i) => (
                  <motion.div
                    key={i}
                    className={`absolute w-2 h-2 rounded-full`}
                    style={{
                      background: i % 3 === 0 ? '#9F0000' : i % 3 === 1 ? '#FF2C2C' : '#FFFFFF',
                      top: `-10%`,
                      left: `${Math.random() * 100}%`
                    }}
                    animate={{
                      y: ['0%', '1000%'],
                      x: [`0%`, `${(Math.random() - 0.5) * 50}%`],
                      rotate: [0, Math.random() * 360],
                      opacity: [1, 0]
                    }}
                    transition={{
                      duration: Math.random() * 3 + 2,
                      delay: Math.random() * 1,
                      repeat: Infinity,
                      repeatDelay: Math.random() * 3
                    }}
                  />
                ))}
              </div>
            </div>
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mb-20"
          >
            <div className="bg-[#1A1A1A]/90 backdrop-blur-md border border-primary/30 rounded-2xl p-8 shadow-glow relative overflow-hidden">
              {/* Decorative elements */}
              <div className="absolute top-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-primary to-transparent"></div>
              <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-primary to-transparent"></div>
              <div className="absolute top-0 left-0 w-[1px] h-full bg-gradient-to-b from-transparent via-primary/20 to-transparent"></div>
              <div className="absolute top-0 right-0 w-[1px] h-full bg-gradient-to-b from-transparent via-primary/20 to-transparent"></div>

              <div className="flex justify-between items-center mb-8">
                <motion.h2
                  className="text-2xl md:text-3xl font-orbitron font-bold text-accent"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-red-500 to-red-700">
                    Wallet Connected
                  </span>
                </motion.h2>

                <motion.button
                  className="text-contrast/80 hover:text-accent transition-all duration-300 bg-primary/10 px-4 py-2 rounded-md hover:bg-primary/20 border border-primary/30"
                  onClick={disconnectWallet}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  Disconnect
                </motion.button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <div className="bg-background/30 p-6 rounded-xl border border-secondary/30 mb-6 shadow-sm">
                    <div className="flex items-center mb-4">
                      <motion.div
                        className="w-14 h-14 mr-4 flex items-center justify-center bg-primary/10 rounded-full relative"
                        animate={{
                          boxShadow: ["0 0 0px rgba(159, 0, 0, 0.3)", "0 0 10px rgba(159, 0, 0, 0.5)", "0 0 0px rgba(159, 0, 0, 0.3)"]
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <div className="absolute inset-0 rounded-full bg-primary/5 blur-md"></div>
                        <img src={selectedWallet?.icon} alt={selectedWallet?.name} className="w-8 h-8 relative z-10" />
                      </motion.div>

                      <div>
                        <h3 className="text-lg font-medium text-white">{selectedWallet?.name}</h3>
                        <div className="flex items-center">
                          <span className="inline-block w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                          <p className="text-sm text-green-400">Connected</p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-contrast/70 mb-2">Wallet Address</h3>
                      <div className="flex items-center bg-background/50 p-2 rounded-lg border border-secondary/30">
                        <code className="text-contrast flex-grow overflow-x-auto text-xs">
                          {walletAddress}
                        </code>
                        <motion.button
                          className="ml-2 p-2 bg-primary/20 text-accent rounded-md hover:bg-primary/30 transition-colors"
                          onClick={() => {
                            navigator.clipboard.writeText(walletAddress);
                            // Show a toast or some feedback
                          }}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                          </svg>
                        </motion.button>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <motion.div
                      className="bg-background/30 p-5 rounded-xl border border-secondary/30 shadow-sm relative overflow-hidden group"
                      whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.3)" }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-br from-secondary/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <h3 className="text-sm font-medium text-contrast/70 mb-1">BNB Balance</h3>
                      <p className="text-2xl font-orbitron text-white">{walletBalance?.bnb} BNB</p>
                    </motion.div>

                    <motion.div
                      className="bg-background/30 p-5 rounded-xl border border-secondary/30 shadow-sm relative overflow-hidden group"
                      whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.3)" }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-br from-secondary/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <h3 className="text-sm font-medium text-contrast/70 mb-1">USDT Balance</h3>
                      <p className="text-2xl font-orbitron text-white">${walletBalance?.usdt}</p>
                    </motion.div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  className="bg-background/20 p-6 rounded-xl border border-primary/20 shadow-sm"
                >
                  <h3 className="text-2xl font-orbitron font-bold text-white mb-6 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    Join ETERNAL
                  </h3>

                  <div className="mb-6">
                    <label htmlFor="referrer" className="block text-contrast mb-2 font-medium">Referrer Address (Optional)</label>
                    <input
                      type="text"
                      id="referrer"
                      className="w-full bg-background/50 border border-secondary/30 rounded-lg p-3 text-contrast focus:border-primary/50 focus:outline-none focus:ring-1 focus:ring-primary/50 transition-all duration-300"
                      placeholder="0x..."
                      value={referrerAddress}
                      onChange={(e) => setReferrerAddress(e.target.value)}
                    />
                  </div>

                  <div className="mb-8 bg-background/30 p-4 rounded-lg border border-secondary/30">
                    <h4 className="text-accent font-medium mb-3">Transaction Details</h4>
                    <div className="flex justify-between items-center mb-3 border-b border-secondary/20 pb-2">
                      <span className="text-contrast">Join Fee</span>
                      <span className="text-white font-medium">$6.00 USDT</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-contrast">Gas Fee (estimated)</span>
                      <span className="text-white font-medium">~0.001 BNB</span>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {!isApproved ? (
                      <motion.button
                        className="w-full bg-secondary text-white font-orbitron font-bold py-3 px-6 rounded-lg hover:bg-secondary/80 transition-all duration-300 shadow-lg border border-secondary/50 flex items-center justify-center"
                        onClick={approveUSDT}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Approve USDT
                      </motion.button>
                    ) : (
                      <motion.button
                        className="w-full px-6 py-4 bg-primary text-white font-orbitron font-bold rounded-lg hover:bg-primary/80 transition-all duration-300 shadow-glow border border-primary/50 flex items-center justify-center"
                        onClick={joinSystem}
                        disabled={isJoining}
                        whileHover={isJoining ? {} : { scale: 1.02 }}
                        whileTap={isJoining ? {} : { scale: 0.98 }}
                      >
                        {isJoining ? (
                          <span className="flex items-center justify-center">
                            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                          </span>
                        ) : (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            Join ETERNAL ($6.00)
                          </>
                        )}
                      </motion.button>
                    )}
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Requirements */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mb-20"
        >
          <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-8 text-center">
            Requirements
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-background border border-secondary/30 rounded-lg p-6">
              <div className="text-primary text-4xl mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                </svg>
              </div>
              <h3 className="text-xl font-orbitron font-bold text-white mb-2">BSC Network</h3>
              <p className="text-contrast/80">
                Make sure your wallet is configured for Binance Smart Chain (BSC). Network settings: Chain ID 56, RPC URL: https://bsc-dataseed.binance.org/
              </p>
            </div>

            <div className="bg-background border border-secondary/30 rounded-lg p-6">
              <div className="text-primary text-4xl mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-orbitron font-bold text-white mb-2">USDT (BEP20)</h3>
              <p className="text-contrast/80">
                You need at least $6 worth of USDT on the Binance Smart Chain (BEP20 standard). Contract address: 0x55d398326f99059fF775485246999027B3197955
              </p>
            </div>

            <div className="bg-background border border-secondary/30 rounded-lg p-6">
              <div className="text-primary text-4xl mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-orbitron font-bold text-white mb-2">BNB for Gas</h3>
              <p className="text-contrast/80">
                You need a small amount of BNB to cover transaction fees (approximately 0.001 BNB per transaction).
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default ConnectWallet
