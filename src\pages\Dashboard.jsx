import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';

// Dashboard Components
import HeaderBar from '../components/dashboard/HeaderBar';
import UserHeader from '../components/dashboard/UserHeader';
import StatsCards from '../components/dashboard/StatsCards';
import LevelProgress from '../components/dashboard/LevelProgress';
import ReferralLink from '../components/dashboard/ReferralLink';
import WeeklyIncome from '../components/dashboard/WeeklyIncome';
import IncomeHistory from '../components/dashboard/IncomeHistory';
// Bottom nav removed

// Dashboard Pages
import Income from './Income';
import Referrals from './Referrals';
import Matrix from './Matrix';
import Profile from './Profile';
import Help from './Help';

// Mock Data
import mockUserData from '../data/mockUserData';

const Dashboard = () => {
  const location = useLocation();
  const [userData, setUserData] = useState(mockUserData);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('weekly');

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Dashboard Overview Component
  const DashboardOverview = () => {
    return (
      <div className="md:grid md:grid-cols-12 md:gap-6 space-y-6 md:space-y-0">
        {/* Left Column - Main Content */}
        <div className="md:col-span-8 space-y-6">
          {/* User Header with ID and Level */}
          <UserHeader userData={userData} />

          {/* Stats Cards Section */}
          <section className="bg-secondary rounded-lg p-4 border border-primary/20">
            <h2 className="text-sm text-white/70 mb-3 flex items-center">
              <span className="w-1 h-4 bg-primary mr-2"></span>
              Total Earnings
            </h2>
            <StatsCards userData={userData} />
          </section>

          {/* Level Progress Section */}
          <section className="bg-secondary rounded-lg p-4 border border-primary/20">
            <h2 className="text-sm text-white/70 mb-3 flex items-center">
              <span className="w-1 h-4 bg-primary mr-2"></span>
              Level Progress
            </h2>
            <LevelProgress userData={userData} />
          </section>

          {/* Income History */}
          <section className="bg-secondary rounded-lg p-4 border border-primary/20">
            <h2 className="text-sm text-white/70 mb-3 flex items-center">
              <span className="w-1 h-4 bg-primary mr-2"></span>
              Income History
            </h2>

            <div className="mb-4">
              <div className="flex space-x-4 border-b border-white/10 mb-4">
                <button
                  className={`pb-2 text-sm ${activeTab === 'weekly' ? 'text-primary border-b-2 border-primary' : 'text-white/50'}`}
                  onClick={() => setActiveTab('weekly')}
                >
                  Weekly
                </button>
                <button
                  className={`pb-2 text-sm ${activeTab === 'monthly' ? 'text-primary border-b-2 border-primary' : 'text-white/50'}`}
                  onClick={() => setActiveTab('monthly')}
                >
                  Monthly
                </button>
              </div>

              <IncomeHistory userData={userData} timeframe={activeTab} />
            </div>
          </section>
        </div>

        {/* Right Column - Sidebar */}
        <div className="md:col-span-4 space-y-6">
          {/* Referral Link Section */}
          <section className="bg-secondary rounded-lg p-4 border border-primary/20">
            <h2 className="text-sm text-white/70 mb-3 flex items-center">
              <span className="w-1 h-4 bg-primary mr-2"></span>
              Your Referral Link
            </h2>
            <ReferralLink userData={userData} />
          </section>

          {/* Weekly Income Distribution */}
          <section className="bg-secondary rounded-lg p-4 border border-primary/20">
            <h2 className="text-sm text-white/70 mb-3 flex items-center">
              <span className="w-1 h-4 bg-primary mr-2"></span>
              Weekly Income Distribution
            </h2>
            <WeeklyIncome userData={userData} />
          </section>
        </div>
      </div>
    );
  };

  // Income View Component
  const IncomeView = () => {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-white">Income Analytics</h2>
        <p className="text-[#999]">This page is under construction.</p>
      </div>
    );
  };

  // Referrals View Component
  const ReferralsView = () => {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-white">Referrals</h2>
        <p className="text-[#999]">This page is under construction.</p>
      </div>
    );
  };

  // Profile View Component
  const ProfileView = () => {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-white">Your Profile</h2>
        <p className="text-[#999]">This page is under construction.</p>
      </div>
    );
  };

  return (
    <div className="flex flex-col min-h-screen bg-background text-white">
      {/* Background effects */}
      <div className="fixed inset-0 z-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-black to-background opacity-90"></div>
        {/* Animated glow effects */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full bg-primary/5 filter blur-[100px] animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full bg-primary/5 filter blur-[100px] animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>

      {/* Header Bar */}
      <HeaderBar userData={userData} />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col relative z-10 w-full">
        {/* Content Area - Responsive container */}
        <div className="flex-1 p-4 md:p-6 overflow-x-hidden max-w-7xl mx-auto w-full">
          {isLoading ? (
            <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
              <div className="relative">
                <div className="absolute inset-0 bg-primary/20 rounded-full blur-xl"></div>
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary relative z-10"></div>
              </div>
            </div>
          ) : (
            <div className="w-full">
              <Routes>
                <Route path="/" element={<DashboardOverview />} />
                <Route path="/income" element={<Income />} />
                <Route path="/referrals" element={<Referrals />} />
                <Route path="/matrix" element={<Matrix />} />
                <Route path="/profile" element={<Profile />} />
                <Route path="/help" element={<Help />} />
                <Route path="*" element={<Navigate to="/dashboard" replace />} />
              </Routes>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
