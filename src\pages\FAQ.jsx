import { useState } from 'react'
import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'

const FAQ = () => {
  const [openQuestion, setOpenQuestion] = useState(null)
  
  const toggleQuestion = (index) => {
    if (openQuestion === index) {
      setOpenQuestion(null)
    } else {
      setOpenQuestion(index)
    }
  }
  
  const faqItems = [
    {
      question: "What is the ETERNAL Income Matrix System?",
      answer: "The ETERNAL Income Matrix System is a revolutionary 4×2 recursive spillover matrix plan that creates weekly income opportunities for members. With just a $6 one-time joining fee, you can start building your team and earning from direct referrals, spillovers, and the weekly Eternal Fund distribution."
    },
    {
      question: "How much does it cost to join?",
      answer: "It costs just $6 USD (paid in USDT on the Binance Smart Chain) to join the ETERNAL Income Matrix System. This is a one-time fee, and there are no recurring costs or hidden fees."
    },
    {
      question: "How is the $6 joining fee distributed?",
      answer: "The $6 joining fee is distributed as follows: $2 goes to the person who referred you (direct referral income), $2 goes to the person under whom you're placed in the matrix (spillover income), and $2 goes to the Eternal Weekly Fund, which is distributed to active members every Sunday."
    },
    {
      question: "What is the 4×2 recursive spillover system?",
      answer: "In our 4×2 matrix system, each member can directly refer unlimited people. For every 4 referrals, a new set is created. In each set of 4 referrals, 2 stay under you and 2 spill over to your upline. This recursive spillover continues throughout the system, creating a powerful network effect that benefits everyone."
    },
    {
      question: "How do I earn income in the ETERNAL system?",
      answer: "There are three ways to earn income: 1) Direct Referral Income: $2 for each person you directly refer, 2) Spillover Income: $2 for each person placed under you through spillover, and 3) Weekly Income: A share of the Eternal Fund distributed every Sunday based on your level and activity."
    },
    {
      question: "What determines my level in the system?",
      answer: "Your level is determined by the number of direct referrals you have. Level 1 requires 4 direct referrals, Level 2 requires 8, Level 3 requires 16, and so on, with each level requiring twice as many referrals as the previous level. There are 10 levels in total."
    },
    {
      question: "How is the weekly fund distributed?",
      answer: "The Eternal Weekly Fund is distributed every Sunday to active members. Your share of the fund depends on your level, with higher levels receiving a larger percentage. Level 1 receives 10%, Level 2 receives 20%, and so on, with Level 10 receiving 100%."
    },
    {
      question: "What does it mean to be 'active' in the system?",
      answer: "To be considered active and eligible for the weekly fund distribution, you must refer at least 1 person during that week. This encourages continuous team building and ensures that the system remains dynamic."
    },
    {
      question: "What blockchain is ETERNAL built on?",
      answer: "ETERNAL is built on the Binance Smart Chain (BSC), which offers fast transactions and low fees. All transactions are secured by blockchain technology, ensuring transparency and immutability of records."
    },
    {
      question: "What wallet do I need to join ETERNAL?",
      answer: "You need a BSC-compatible wallet such as MetaMask, Trust Wallet, SafePal, or TokenPocket. Your wallet must be configured for the Binance Smart Chain network."
    },
    {
      question: "What tokens do I need to join?",
      answer: "You need $6 worth of USDT on the Binance Smart Chain (BEP20 standard) to join. You also need a small amount of BNB (approximately 0.001 BNB) to cover transaction fees."
    },
    {
      question: "Is the smart contract verified and secure?",
      answer: "Yes, our smart contract has been verified on BSCScan and audited by independent security firms to ensure it's secure and functions as intended. Once deployed, the contract's core rules cannot be changed, ensuring that the system operates exactly as promised."
    },
    {
      question: "How do spillovers work in practice?",
      answer: "When you refer 4 people (let's call them A, B, C, and D), 2 of them (e.g., A and C) stay under you, while the other 2 (B and D) spill over to your upline. When B and D refer people, some of their referrals will also spill over to their upline, which could include you. This recursive process continues throughout the system."
    },
    {
      question: "Can I see my team structure?",
      answer: "Yes, you can view your team structure in the Team Tree section of the website. This visual representation shows your direct referrals and spillovers, helping you understand how your team is growing."
    },
    {
      question: "How do I refer new members?",
      answer: "You can refer new members by sharing your unique referral link, which can be found in your Dashboard. When someone joins using your referral link, you'll receive $2 in direct referral income."
    }
  ]
  
  return (
    <div className="py-16 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-orbitron font-bold text-primary mb-6">Frequently Asked Questions</h1>
          <p className="text-xl text-contrast/80 max-w-3xl mx-auto">
            Find answers to common questions about the ETERNAL Income Matrix System
          </p>
        </motion.div>
        
        {/* FAQ Accordion */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mb-20"
        >
          <div className="bg-secondary/20 backdrop-blur-sm border border-secondary/30 rounded-lg p-6 neon-border">
            <div className="space-y-4">
              {faqItems.map((item, index) => (
                <div 
                  key={index}
                  className="border-b border-secondary/30 last:border-b-0"
                >
                  <button
                    className="flex justify-between items-center w-full py-4 text-left focus:outline-none"
                    onClick={() => toggleQuestion(index)}
                  >
                    <h3 className="text-lg font-orbitron font-bold text-white">{item.question}</h3>
                    <svg
                      className={`h-6 w-6 text-accent transition-transform duration-300 ${
                        openQuestion === index ? 'transform rotate-180' : ''
                      }`}
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                  
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{
                      height: openQuestion === index ? 'auto' : 0,
                      opacity: openQuestion === index ? 1 : 0,
                    }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="py-4 text-contrast/90">
                      {item.answer}
                    </div>
                  </motion.div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
        
        {/* Contact Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mb-20"
        >
          <div className="bg-primary/10 backdrop-blur-sm border border-primary/30 rounded-lg p-8 text-center">
            <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-white mb-6">
              Still Have Questions?
            </h2>
            <p className="text-xl text-contrast/90 max-w-3xl mx-auto mb-8">
              Join our community channels for more information and support.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <a 
                href="#" 
                className="bg-[#0088cc]/20 text-white font-orbitron font-bold py-2 px-6 rounded-md hover:bg-[#0088cc]/30 transition-all duration-300 shadow-lg flex items-center"
              >
                <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm0 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm2.692 14.889c.161.12.36.181.557.181.212 0 .527-.038.723-.38.212-.177.339-.422.339-.675 0-.253-.126-.497-.339-.675l-1.339-1.166 1.339-1.167c.212-.177.339-.422.339-.675s-.126-.498-.339-.675c-.196-.177-.511-.255-.723-.255-.197 0-.396.078-.557.255l-2.108 1.77c-.292.244-.438.511-.438.747 0 .235.146.502.438.746l2.108 1.771zm-5.384-7.778c-.197-.177-.511-.255-.723-.255-.197 0-.396.078-.557.255l-2.108 1.77c-.292.244-.438.511-.438.747 0 .235.146.502.438.746l2.108 1.771c.161.12.36.181.557.181.212 0 .527-.038.723-.38.212-.177.339-.422.339-.675 0-.253-.126-.497-.339-.675l-1.339-1.166 1.339-1.167c.212-.177.339-.422.339-.675s-.126-.498-.339-.675zm11.027 2.517c0 1.493-1.211 2.704-2.704 2.704h-10.261c-1.493 0-2.704-1.211-2.704-2.704v-5.37c0-1.494 1.211-2.705 2.704-2.705h10.261c1.493 0 2.704 1.211 2.704 2.705v5.37z"/>
                </svg>
                Telegram Community
              </a>
              <a 
                href="#" 
                className="bg-[#7289da]/20 text-white font-orbitron font-bold py-2 px-6 rounded-md hover:bg-[#7289da]/30 transition-all duration-300 shadow-lg flex items-center"
              >
                <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z"/>
                </svg>
                Discord Server
              </a>
              <a 
                href="mailto:<EMAIL>" 
                className="bg-secondary/20 text-white font-orbitron font-bold py-2 px-6 rounded-md hover:bg-secondary/30 transition-all duration-300 shadow-lg flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                Email Support
              </a>
            </div>
          </div>
        </motion.div>
        
        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="text-center"
        >
          <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-6">
            Ready to Start Your ETERNAL Journey?
          </h2>
          <p className="text-xl text-contrast/80 max-w-3xl mx-auto mb-8">
            Join now with just $6 and become part of the revolutionary income system that's changing lives.
          </p>
          <Link 
            to="/connect-wallet" 
            className="btn-primary text-lg px-8 py-3"
          >
            Connect Wallet & Join
          </Link>
        </motion.div>
      </div>
    </div>
  )
}

export default FAQ
