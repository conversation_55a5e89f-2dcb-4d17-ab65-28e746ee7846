import React, { useState } from 'react';
import { motion } from 'framer-motion';

// Mock Data
import mockUserData from '../data/mockUserData';

const Help = () => {
  const [activeCategory, setActiveCategory] = useState('getting-started');
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedFaq, setExpandedFaq] = useState(null);

  // FAQ categories
  const categories = [
    { id: 'getting-started', name: 'Getting Started' },
    { id: 'matrix', name: 'Matrix System' },
    { id: 'earnings', name: 'Earnings & Payouts' },
    { id: 'referrals', name: 'Referrals' },
    { id: 'technical', name: 'Technical Support' },
  ];

  // FAQ data
  const faqData = {
    'getting-started': [
      {
        id: 1,
        question: 'What is Eternal Income Matrix?',
        answer: 'Eternal Income Matrix is a decentralized matrix-based income system built on the BSC blockchain. It allows participants to earn passive income through a recursive spillover matrix structure, where earnings are generated from both direct referrals and team growth.'
      },
      {
        id: 2,
        question: 'How do I get started?',
        answer: 'To get started, you need to connect your BSC wallet (like MetaMask or Trust Wallet), pay the one-time entry fee of $6, and you\'ll be automatically enrolled in the system. You\'ll then have access to your dashboard where you can track your earnings and matrix progress.'
      },
      {
        id: 3,
        question: 'What is the minimum investment?',
        answer: 'The minimum investment is a one-time fee of $6 paid in BNB on the Binance Smart Chain. This gives you lifetime access to the Eternal Income Matrix system.'
      },
      {
        id: 4,
        question: 'Is there a maximum earning potential?',
        answer: 'There is no cap on your earning potential. As your matrices fill and your team grows, your income can scale indefinitely. The system is designed to provide both immediate and long-term passive income.'
      },
    ],
    'matrix': [
      {
        id: 5,
        question: 'How does the matrix system work?',
        answer: 'The Eternal Matrix System uses a recursive spillover structure with multiple matrix types (X3, X4, X5, X6). Each matrix has multiple levels, and when your matrix fills up, you earn commissions and spillover to your upline. As more people join under you, your matrices fill faster, generating more income.'
      },
      {
        id: 6,
        question: 'What is the difference between the matrix types?',
        answer: 'Each matrix type has a different structure and payout system: X3 is a 3x1 matrix with 3 positions per level, X4 is a 2x2 matrix with 4 positions per level, X5 is a 5x1 matrix with 5 positions per level, and X6 is a 3x2 matrix with 6 positions per level. Higher matrix types generally offer larger payouts but require more positions to be filled.'
      },
      {
        id: 7,
        question: 'What happens when my matrix is full?',
        answer: 'When your matrix is full at one level, you automatically progress to the next level of that matrix. This creates a continuous income stream as your matrices continue to fill and cycle through the levels.'
      },
      {
        id: 8,
        question: 'Do I need to manually upgrade my matrix levels?',
        answer: 'No, the system automatically upgrades your matrix levels as they fill. There are no manual upgrades or additional payments required after your initial entry fee.'
      },
    ],
    'earnings': [
      {
        id: 9,
        question: 'How are earnings calculated?',
        answer: 'Earnings are calculated based on the matrix structure and your position within it. You earn from direct referrals, spillovers from your upline, and matrix completions. The exact amount depends on the matrix type and level.'
      },
      {
        id: 10,
        question: 'When are earnings distributed?',
        answer: 'Earnings are distributed in real-time directly to your connected wallet as soon as a position in your matrix is filled. Additionally, there is a weekly pool distribution every Sunday for active members.'
      },
      {
        id: 11,
        question: 'What is the weekly pool distribution?',
        answer: '5% of all entry fees are added to a weekly pool that is distributed among active members every Sunday. Your share of the pool is determined by your activity level and matrix performance during that week.'
      },
      {
        id: 12,
        question: 'How do I withdraw my earnings?',
        answer: 'Your earnings are automatically sent to your connected BSC wallet. There is no need for manual withdrawals. You can view your transaction history in the Income section of your dashboard.'
      },
    ],
    'referrals': [
      {
        id: 13,
        question: 'How do I refer others?',
        answer: 'You can refer others by sharing your unique referral link found in your dashboard. When someone joins using your link, they are automatically placed in your matrix, and you earn referral commissions.'
      },
      {
        id: 14,
        question: 'What are the referral bonuses?',
        answer: 'You earn direct referral bonuses when someone joins using your link. The exact amount depends on the matrix level and type. Additionally, you benefit from the growth of your entire team through the matrix structure.'
      },
      {
        id: 15,
        question: 'Do I need to refer others to earn?',
        answer: 'While referring others will accelerate your earnings, you can still earn through spillovers from your upline and the weekly pool distribution even without direct referrals. However, to maximize your income potential, building a team is recommended.'
      },
      {
        id: 16,
        question: 'Is there a limit to how many people I can refer?',
        answer: 'There is no limit to the number of people you can refer. The more people you refer, the faster your matrices will fill and the more income you can generate.'
      },
    ],
    'technical': [
      {
        id: 17,
        question: 'Which wallets are supported?',
        answer: 'We support all major BSC-compatible wallets including MetaMask, Trust Wallet, Binance Chain Wallet, and WalletConnect. Make sure your wallet is configured for the Binance Smart Chain network.'
      },
      {
        id: 18,
        question: 'I\'m having trouble connecting my wallet, what should I do?',
        answer: 'First, ensure your wallet is configured for the BSC network. If you\'re still having issues, try refreshing the page, clearing your browser cache, or using a different browser. If problems persist, contact our support team through the help desk.'
      },
      {
        id: 19,
        question: 'Are my transactions secure?',
        answer: 'Yes, all transactions are secured by the Binance Smart Chain blockchain. Every transaction is transparent and can be verified on the BSC explorer. We do not have access to your private keys or funds.'
      },
      {
        id: 20,
        question: 'How can I contact support?',
        answer: 'You can contact our support team through the help desk in your dashboard, join our Telegram community, or send an <NAME_EMAIL>. Our team is available 24/7 to assist you.'
      },
    ],
  };

  // Filter FAQs based on search term
  const getFilteredFaqs = () => {
    if (!searchTerm) {
      return faqData[activeCategory];
    }

    const searchTermLower = searchTerm.toLowerCase();
    const allFaqs = Object.values(faqData).flat();

    return allFaqs.filter(faq =>
      faq.question.toLowerCase().includes(searchTermLower) ||
      faq.answer.toLowerCase().includes(searchTermLower)
    );
  };

  const filteredFaqs = getFilteredFaqs();

  return (
    <div className="flex flex-col min-h-screen bg-background text-white">
      {/* Background effects */}
      <div className="fixed inset-0 z-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-black to-background opacity-90"></div>
        {/* Animated glow effects */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full bg-primary/5 filter blur-[100px] animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full bg-primary/5 filter blur-[100px] animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>


      {/* Main Content */}
      <div className="flex-1 flex flex-col relative z-10 w-full">
        <div className="flex-1 p-4 md:p-6 overflow-x-hidden max-w-7xl mx-auto w-full">
          <div className="md:grid md:grid-cols-12 md:gap-6 space-y-6 md:space-y-0">
            {/* Sidebar Column */}
            <div className="md:col-span-4 space-y-6">
              {/* Help Center Card */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <h1 className="text-2xl font-bold text-white mb-2">Help Center</h1>
                <p className="text-white/70 mb-4">Find answers to your questions about the Eternal Income Matrix System</p>

                <div className="relative mb-4">
                  <input
                    type="text"
                    placeholder="Search for help..."
                    className="w-full bg-background text-white text-sm border border-primary/20 rounded-lg px-4 py-2 pl-10 focus:outline-none focus:border-primary"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white/50 absolute left-3 top-1/2 transform -translate-y-1/2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>

                <div className="space-y-2">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      className={`w-full text-left px-3 py-2 rounded-lg text-sm ${
                        activeCategory === category.id && !searchTerm
                          ? 'bg-primary text-white'
                          : 'bg-background text-white/70 hover:bg-background/80'
                      }`}
                      onClick={() => {
                        setActiveCategory(category.id);
                        setSearchTerm('');
                      }}
                    >
                      {category.name}
                    </button>
                  ))}
                </div>
              </motion.div>

              {/* Contact Support */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <h2 className="text-lg font-bold text-white mb-2">Need More Help?</h2>
                <p className="text-white/70 mb-4">Our support team is available 24/7 to assist you with any questions or issues.</p>

                <div className="space-y-3">
                  <a href="#" className="flex items-center bg-background rounded-lg p-3 border border-primary/20 hover:border-primary transition-colors">
                    <div className="bg-primary/20 rounded-full p-2 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-white text-sm font-medium">Email Support</div>
                      <div className="text-white/50 text-xs"><EMAIL></div>
                    </div>
                  </a>

                  <a href="#" className="flex items-center bg-background rounded-lg p-3 border border-primary/20 hover:border-primary transition-colors">
                    <div className="bg-primary/20 rounded-full p-2 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-white text-sm font-medium">Live Chat</div>
                      <div className="text-white/50 text-xs">Chat with our support team</div>
                    </div>
                  </a>

                  <a href="#" className="flex items-center bg-background rounded-lg p-3 border border-primary/20 hover:border-primary transition-colors">
                    <div className="bg-primary/20 rounded-full p-2 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-white text-sm font-medium">Telegram Community</div>
                      <div className="text-white/50 text-xs">Join our Telegram group</div>
                    </div>
                  </a>
                </div>
              </motion.div>
            </div>

            {/* Main Content Column */}
            <div className="md:col-span-8 space-y-6">
              {/* FAQ Section */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <h2 className="text-lg font-bold text-white mb-4">
                  {searchTerm ? 'Search Results' : `${categories.find(c => c.id === activeCategory).name} FAQs`}
                </h2>

                {filteredFaqs.length > 0 ? (
                  <div className="space-y-3">
                    {filteredFaqs.map((faq) => (
                      <div
                        key={faq.id}
                        className="bg-background rounded-lg border border-primary/20 overflow-hidden"
                      >
                        <button
                          className="w-full text-left p-4 flex justify-between items-center"
                          onClick={() => setExpandedFaq(expandedFaq === faq.id ? null : faq.id)}
                        >
                          <h3 className="text-white text-sm font-medium">{faq.question}</h3>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className={`h-5 w-5 text-white/50 transition-transform ${expandedFaq === faq.id ? 'transform rotate-180' : ''}`}
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </button>

                        {expandedFaq === faq.id && (
                          <motion.div
                            className="p-4 pt-0 text-white/70 text-sm border-t border-primary/10"
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            transition={{ duration: 0.3 }}
                          >
                            <p>{faq.answer}</p>
                          </motion.div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="bg-background rounded-lg p-6 border border-primary/20 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-white/30 mx-auto mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 className="text-white text-lg font-medium mb-1">No results found</h3>
                    <p className="text-white/50 text-sm">
                      We couldn't find any FAQs matching your search. Try different keywords or browse the categories.
                    </p>
                  </div>
                )}
              </motion.div>

              {/* Video Tutorials */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <h2 className="text-lg font-bold text-white mb-4">Video Tutorials</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-background rounded-lg border border-primary/20 overflow-hidden">
                    <div className="aspect-video bg-black/50 relative">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div className="p-3">
                      <h3 className="text-white text-sm font-medium">Getting Started Guide</h3>
                      <p className="text-white/50 text-xs mt-1">Learn how to set up your account and start earning</p>
                    </div>
                  </div>

                  <div className="bg-background rounded-lg border border-primary/20 overflow-hidden">
                    <div className="aspect-video bg-black/50 relative">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div className="p-3">
                      <h3 className="text-white text-sm font-medium">Matrix System Explained</h3>
                      <p className="text-white/50 text-xs mt-1">Understand how the matrix system works</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Help;
