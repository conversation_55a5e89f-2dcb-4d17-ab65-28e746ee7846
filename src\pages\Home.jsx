import { useState, useEffect, useRef } from 'react'
import { Link } from 'react-router-dom'
import { motion, useScroll, useTransform } from 'framer-motion'

// Components
import ParticleBackground from '../components/ParticleBackground'
import eternalLogo from '../assets/images/Eternal logo r.png'

const Home = () => {
  const [memberCount, setMemberCount] = useState(0)
  const [fundDistributed, setFundDistributed] = useState(0)
  const [activeMembers, setActiveMembers] = useState(0)

  // Refs for scroll animations
  const heroRef = useRef(null)
  const featuresRef = useRef(null)
  const ctaRef = useRef(null)

  // Scroll animations
  const { scrollYProgress } = useScroll()
  const heroOpacity = useTransform(scrollYProgress, [0, 0.2], [1, 0.2])
  const heroScale = useTransform(scrollYProgress, [0, 0.2], [1, 0.95])

  // Simulate data fetching with animated counters
  useEffect(() => {
    const targetMemberCount = 10000
    const targetFundDistributed = 50000
    const targetActiveMembers = 5000

    const duration = 2000 // 2 seconds for initial animation
    const frameDuration = 1000/60 // 60fps
    const totalFrames = Math.round(duration / frameDuration)

    let frame = 0

    const counter = setInterval(() => {
      frame++

      const progress = frame / totalFrames
      const easeProgress = progress < 0.5
        ? 4 * progress * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 3) / 2 // Cubic easing

      setMemberCount(Math.floor(easeProgress * targetMemberCount))
      setFundDistributed(Math.floor(easeProgress * targetFundDistributed))
      setActiveMembers(Math.floor(easeProgress * targetActiveMembers))

      if (frame === totalFrames) {
        clearInterval(counter)

        // After initial animation, start random increments
        const randomInterval = setInterval(() => {
          setMemberCount(prev => Math.min(prev + Math.floor(Math.random() * 5), targetMemberCount + 500))
          setFundDistributed(prev => Math.min(prev + Math.floor(Math.random() * 100), targetFundDistributed + 2000))
          setActiveMembers(prev => Math.min(prev + Math.floor(Math.random() * 3), targetActiveMembers + 200))
        }, 3000)

        return () => clearInterval(randomInterval)
      }
    }, frameDuration)

    return () => clearInterval(counter)
  }, [])

  return (
    <div className="relative min-h-screen">
      <ParticleBackground />

      {/* Hero Section */}
      <motion.section
        ref={heroRef}
        className="relative pt-32 pb-20 overflow-hidden min-h-screen flex items-center"
        style={{ opacity: heroOpacity, scale: heroScale }}
      >
        {/* Background with dark overlay */}
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-b from-[#000000] via-[#0A0A0A] to-[#000000] opacity-90"></div>
          {/* Red accent lines */}
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#9F0000] to-transparent opacity-70"></div>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#9F0000] to-transparent opacity-70"></div>
          {/* Animated glow effects */}
          <div className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full bg-[#9F0000]/10 filter blur-[100px] animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full bg-[#9F0000]/10 filter blur-[100px] animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <motion.div
              className="flex justify-center mb-8"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{
                duration: 1.2,
                type: "spring",
                bounce: 0.4
              }}
            >
              <div className="relative logo-container">
                {/* Logo glow effect */}
                <div className="absolute inset-0 bg-[#9F0000]/20 filter blur-xl rounded-full scale-150"></div>
                <img
                  src={eternalLogo}
                  alt="ETERNAL Logo"
                  className="h-32 md:h-40 w-auto logo-image relative z-10"
                  style={{ background: 'transparent' }}
                />
              </div>
            </motion.div>

            <motion.h1
              className="text-5xl md:text-7xl font-orbitron font-bold text-[#FF2C2C] mb-6"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <span className="neon-glow relative">
                ETERNAL
                {/* Text underline effect */}
                <span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-[#FF2C2C] to-transparent"></span>
              </span>
            </motion.h1>

            <motion.p
              className="text-2xl md:text-4xl font-orbitron mb-8"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#9F0000] to-[#FF2C2C]">
                INCOME MATRIX SYSTEM
              </span>
            </motion.p>

            <motion.p
              className="text-lg md:text-xl text-[#BFBFBF]/90 max-w-3xl mx-auto mb-12"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              The revolutionary <span className="text-[#FF2C2C] font-bold">4×2 recursive spillover system</span> that generates passive income weekly.
              Join with just <span className="text-[#FF2C2C] font-bold">$6</span> and start earning from the Eternal Income Fund.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-col sm:flex-row justify-center gap-4"
            >
              <Link
                to="/connect-wallet"
                className="px-8 py-4 bg-[#9F0000] text-white font-orbitron font-bold rounded-md hover:bg-[#FF2C2C] transition-all duration-300 shadow-[0_0_15px_rgba(159,0,0,0.5)] border border-[#9F0000]/50 text-lg relative overflow-hidden group"
              >
                <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-[#FF2C2C]/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                <span className="relative z-10">Join Now</span>
              </Link>

              <Link
                to="/how-it-works"
                className="px-8 py-4 bg-[#3A3A3A]/50 text-white font-orbitron font-bold rounded-md hover:bg-[#3A3A3A]/80 transition-all duration-300 border border-[#9F0000]/30 text-lg relative overflow-hidden group"
              >
                <span className="absolute inset-0 w-0 bg-gradient-to-r from-[#9F0000]/20 to-transparent group-hover:w-full transition-all duration-500"></span>
                <span className="relative z-10">Learn More</span>
              </Link>
            </motion.div>
          </div>

          {/* Stats */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-20"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <motion.div
              className="bg-[#1A1A1A] text-white rounded-2xl p-6 shadow-[0_0_20px_rgba(159,0,0,0.3)] border border-[#9F0000]/20 relative overflow-hidden group"
              whileHover={{ scale: 1.05, boxShadow: "0 0 30px rgba(159, 0, 0, 0.5)" }}
              transition={{ duration: 0.3 }}
            >
              {/* Background effect */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-[#9F0000]/10 rounded-full -mr-16 -mt-16 z-0 group-hover:scale-150 transition-transform duration-700"></div>
              <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#9F0000] to-transparent opacity-30 group-hover:opacity-70 transition-opacity duration-500"></div>

              <div className="flex flex-col items-center relative z-10">
                <div className="w-16 h-16 mb-4 flex items-center justify-center bg-[#9F0000]/20 rounded-full group-hover:bg-[#9F0000]/30 transition-colors duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#FF2C2C]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-orbitron text-[#FF2C2C] mb-2">Total Members</h3>
                <motion.p
                  className="text-5xl font-bold text-white"
                  initial={{ scale: 0.5, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 1, duration: 0.5, type: "spring" }}
                >
                  {memberCount.toLocaleString()}
                </motion.p>
              </div>
            </motion.div>

            <motion.div
              className="bg-[#1A1A1A] text-white rounded-2xl p-6 shadow-[0_0_20px_rgba(159,0,0,0.3)] border border-[#9F0000]/20 relative overflow-hidden group"
              whileHover={{ scale: 1.05, boxShadow: "0 0 30px rgba(159, 0, 0, 0.5)" }}
              transition={{ duration: 0.3 }}
            >
              {/* Background effect */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-[#9F0000]/10 rounded-full -mr-16 -mt-16 z-0 group-hover:scale-150 transition-transform duration-700"></div>
              <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#9F0000] to-transparent opacity-30 group-hover:opacity-70 transition-opacity duration-500"></div>

              <div className="flex flex-col items-center relative z-10">
                <div className="w-16 h-16 mb-4 flex items-center justify-center bg-[#9F0000]/20 rounded-full group-hover:bg-[#9F0000]/30 transition-colors duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#FF2C2C]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-orbitron text-[#FF2C2C] mb-2">Fund Distributed</h3>
                <motion.p
                  className="text-5xl font-bold text-white"
                  initial={{ scale: 0.5, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 1.2, duration: 0.5, type: "spring" }}
                >
                  ${fundDistributed.toLocaleString()}
                </motion.p>
              </div>
            </motion.div>

            <motion.div
              className="bg-[#1A1A1A] text-white rounded-2xl p-6 shadow-[0_0_20px_rgba(159,0,0,0.3)] border border-[#9F0000]/20 relative overflow-hidden group"
              whileHover={{ scale: 1.05, boxShadow: "0 0 30px rgba(159, 0, 0, 0.5)" }}
              transition={{ duration: 0.3 }}
            >
              {/* Background effect */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-[#9F0000]/10 rounded-full -mr-16 -mt-16 z-0 group-hover:scale-150 transition-transform duration-700"></div>
              <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#9F0000] to-transparent opacity-30 group-hover:opacity-70 transition-opacity duration-500"></div>

              <div className="flex flex-col items-center relative z-10">
                <div className="w-16 h-16 mb-4 flex items-center justify-center bg-[#9F0000]/20 rounded-full group-hover:bg-[#9F0000]/30 transition-colors duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#FF2C2C]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-orbitron text-[#FF2C2C] mb-2">Active This Week</h3>
                <motion.p
                  className="text-5xl font-bold text-white"
                  initial={{ scale: 0.5, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 1.4, duration: 0.5, type: "spring" }}
                >
                  {activeMembers.toLocaleString()}
                </motion.p>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>

      {/* Features Section */}
      <motion.section
        ref={featuresRef}
        className="py-20 relative"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true, amount: 0.2 }}
        transition={{ duration: 0.8 }}
      >
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-b from-[#000000] via-[#9F0000]/5 to-[#000000]"></div>
          {/* Animated accent lines */}
          <div className="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-transparent via-[#9F0000]/30 to-transparent opacity-70"></div>
          <div className="absolute right-0 top-0 w-1 h-full bg-gradient-to-b from-transparent via-[#9F0000]/30 to-transparent opacity-70"></div>
          {/* Animated glow effects */}
          <div className="absolute top-1/3 right-1/4 w-96 h-96 rounded-full bg-[#9F0000]/5 filter blur-[100px] animate-pulse" style={{ animationDelay: '2s' }}></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl md:text-5xl font-orbitron font-bold mb-4 relative inline-block">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#9F0000] to-[#FF2C2C]">
                Why ETERNAL is Better
              </span>
              {/* Animated underline */}
              <span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-[#9F0000] to-transparent"></span>
            </h2>
            <div className="w-24 h-1 bg-[#FF2C2C] mx-auto mb-6 mt-8 opacity-70"></div>
            <p className="text-lg text-[#BFBFBF]/90 max-w-3xl mx-auto">
              Our revolutionary system combines the best elements of matrix plans with innovative recursive spillover technology.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              className="bg-[#1A1A1A] text-white rounded-2xl p-8 shadow-[0_0_20px_rgba(159,0,0,0.3)] border border-[#9F0000]/20 relative overflow-hidden group"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              whileHover={{ y: -5, boxShadow: "0 0 30px rgba(159, 0, 0, 0.5)" }}
            >
              {/* Card background effects */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-[#9F0000]/10 rounded-full -mr-16 -mt-16 z-0 group-hover:scale-150 transition-transform duration-700"></div>
              <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#9F0000] to-transparent opacity-30 group-hover:opacity-70 transition-opacity duration-500"></div>

              <div className="relative z-10">
                <div className="w-16 h-16 mb-6 flex items-center justify-center bg-[#9F0000]/20 rounded-full text-[#FF2C2C] group-hover:bg-[#9F0000]/30 transition-colors duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-orbitron font-bold text-[#FF2C2C] mb-3 group-hover:text-white transition-colors duration-300">Low Entry Cost</h3>
                <p className="text-[#BFBFBF]/90 group-hover:text-white/90 transition-colors duration-300">
                  Join with just $6 and start building your network. No hidden fees or complicated packages.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="bg-[#1A1A1A] text-white rounded-2xl p-8 shadow-[0_0_20px_rgba(159,0,0,0.3)] border border-[#9F0000]/20 relative overflow-hidden group"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
              whileHover={{ y: -5, boxShadow: "0 0 30px rgba(159, 0, 0, 0.5)" }}
            >
              {/* Card background effects */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-[#9F0000]/10 rounded-full -mr-16 -mt-16 z-0 group-hover:scale-150 transition-transform duration-700"></div>
              <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#9F0000] to-transparent opacity-30 group-hover:opacity-70 transition-opacity duration-500"></div>

              <div className="relative z-10">
                <div className="w-16 h-16 mb-6 flex items-center justify-center bg-[#9F0000]/20 rounded-full text-[#FF2C2C] group-hover:bg-[#9F0000]/30 transition-colors duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-orbitron font-bold text-[#FF2C2C] mb-3 group-hover:text-white transition-colors duration-300">Weekly Income</h3>
                <p className="text-[#BFBFBF]/90 group-hover:text-white/90 transition-colors duration-300">
                  Receive your share of the Eternal Fund every Sunday based on your level and activity.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="bg-card text-white rounded-2xl p-8 shadow-glow border border-primary/20 relative overflow-hidden group"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
              whileHover={{ y: -5, boxShadow: "0 0 25px rgba(74, 0, 224, 0.4)" }}
            >
              <div className="absolute top-0 right-0 w-32 h-32 bg-primary/10 rounded-full -mr-16 -mt-16 z-0 group-hover:scale-150 transition-transform duration-700"></div>
              <div className="relative z-10">
                <div className="w-16 h-16 mb-6 flex items-center justify-center bg-primary/20 rounded-full text-accent">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-orbitron font-bold text-white mb-3">Recursive Spillover</h3>
                <p className="text-contrast/80">
                  Benefit from the work of your entire team with our unique recursive spillover system.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="bg-card text-white rounded-2xl p-8 shadow-glow border border-primary/20 relative overflow-hidden group"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.5 }}
              whileHover={{ y: -5, boxShadow: "0 0 25px rgba(74, 0, 224, 0.4)" }}
            >
              <div className="absolute top-0 right-0 w-32 h-32 bg-primary/10 rounded-full -mr-16 -mt-16 z-0 group-hover:scale-150 transition-transform duration-700"></div>
              <div className="relative z-10">
                <div className="w-16 h-16 mb-6 flex items-center justify-center bg-primary/20 rounded-full text-accent">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-orbitron font-bold text-white mb-3">Smart Contract Secured</h3>
                <p className="text-contrast/80">
                  All transactions are secured by blockchain technology with transparent, immutable records.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="bg-card text-white rounded-2xl p-8 shadow-glow border border-primary/20 relative overflow-hidden group"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.6 }}
              whileHover={{ y: -5, boxShadow: "0 0 25px rgba(74, 0, 224, 0.4)" }}
            >
              <div className="absolute top-0 right-0 w-32 h-32 bg-primary/10 rounded-full -mr-16 -mt-16 z-0 group-hover:scale-150 transition-transform duration-700"></div>
              <div className="relative z-10">
                <div className="w-16 h-16 mb-6 flex items-center justify-center bg-primary/20 rounded-full text-accent">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-orbitron font-bold text-white mb-3">Team Building</h3>
                <p className="text-contrast/80">
                  Grow your team and advance through 10 levels with increasing rewards at each milestone.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="bg-card text-white rounded-2xl p-8 shadow-glow border border-primary/20 relative overflow-hidden group"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.7 }}
              whileHover={{ y: -5, boxShadow: "0 0 25px rgba(74, 0, 224, 0.4)" }}
            >
              <div className="absolute top-0 right-0 w-32 h-32 bg-primary/10 rounded-full -mr-16 -mt-16 z-0 group-hover:scale-150 transition-transform duration-700"></div>
              <div className="relative z-10">
                <div className="w-16 h-16 mb-6 flex items-center justify-center bg-primary/20 rounded-full text-accent">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-orbitron font-bold text-white mb-3">Passive Income</h3>
                <p className="text-contrast/80">
                  Once your team is built, enjoy passive income from the system's recursive nature.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* CTA Section */}
      <motion.section
        ref={ctaRef}
        className="py-20 relative"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true, amount: 0.2 }}
        transition={{ duration: 0.8 }}
      >
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-b from-[#000000] via-[#9F0000]/10 to-[#000000]"></div>
          {/* Animated glow effects */}
          <div className="absolute bottom-1/4 left-1/4 w-96 h-96 rounded-full bg-[#9F0000]/10 filter blur-[100px] animate-pulse" style={{ animationDelay: '3s' }}></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="bg-[#1A1A1A]/90 backdrop-blur-md border border-[#9F0000]/30 rounded-2xl p-8 md:p-12 text-center shadow-[0_0_30px_rgba(159,0,0,0.3)] relative overflow-hidden"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <div className="absolute inset-0 z-0">
              <div className="absolute inset-0 bg-gradient-to-r from-[#9F0000]/10 via-transparent to-[#9F0000]/10"></div>
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#9F0000] to-transparent"></div>
              <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#9F0000] to-transparent"></div>
              {/* Animated corner accents */}
              <div className="absolute top-0 left-0 w-16 h-16">
                <div className="absolute top-0 left-0 w-1 h-8 bg-[#9F0000]/70"></div>
                <div className="absolute top-0 left-0 w-8 h-1 bg-[#9F0000]/70"></div>
              </div>
              <div className="absolute top-0 right-0 w-16 h-16">
                <div className="absolute top-0 right-0 w-1 h-8 bg-[#9F0000]/70"></div>
                <div className="absolute top-0 right-0 w-8 h-1 bg-[#9F0000]/70"></div>
              </div>
              <div className="absolute bottom-0 left-0 w-16 h-16">
                <div className="absolute bottom-0 left-0 w-1 h-8 bg-[#9F0000]/70"></div>
                <div className="absolute bottom-0 left-0 w-8 h-1 bg-[#9F0000]/70"></div>
              </div>
              <div className="absolute bottom-0 right-0 w-16 h-16">
                <div className="absolute bottom-0 right-0 w-1 h-8 bg-[#9F0000]/70"></div>
                <div className="absolute bottom-0 right-0 w-8 h-1 bg-[#9F0000]/70"></div>
              </div>
            </div>

            <div className="relative z-10">
              <motion.h2
                className="text-3xl md:text-5xl font-orbitron font-bold text-white mb-6"
                initial={{ scale: 0.9, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                Ready to Start Your <span className="text-[#FF2C2C] neon-glow">ETERNAL</span> Journey?
              </motion.h2>

              <motion.p
                className="text-xl text-[#BFBFBF]/90 max-w-3xl mx-auto mb-10"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                Join now with just <span className="text-[#FF2C2C] font-bold">$6</span> and become part of the revolutionary income system that's changing lives.
              </motion.p>

              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.6 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link
                  to="/connect-wallet"
                  className="px-10 py-4 bg-[#9F0000] text-white font-orbitron font-bold rounded-md hover:bg-[#FF2C2C] transition-all duration-300 shadow-[0_0_20px_rgba(159,0,0,0.5)] border border-[#9F0000]/50 text-lg inline-block relative overflow-hidden group"
                >
                  <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-[#FF2C2C]/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                  <span className="relative z-10">Connect Wallet & Join</span>
                </Link>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </motion.section>
    </div>
  )
}

export default Home
