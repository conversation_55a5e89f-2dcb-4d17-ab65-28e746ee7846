import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import { useRef } from 'react'

const HowItWorks = () => {
  // Refs for scroll animations
  const containerRef = useRef(null)

  return (
    <div className="pt-24 pb-16 bg-background relative overflow-hidden" ref={containerRef}>
      {/* Background effects */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background"></div>
        <div className="absolute inset-0 opacity-5"></div>
      </div>

      {/* Animated particles */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-primary rounded-full shadow-glow"
            initial={{
              x: Math.random() * 100 + "%",
              y: Math.random() * 100 + "%",
              opacity: Math.random() * 0.5 + 0.3
            }}
            animate={{
              y: [null, Math.random() * 100 + "%"],
              opacity: [null, Math.random() * 0.3 + 0.1]
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
        ))}
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <motion.div
            className="inline-block relative mb-6"
            animate={{
              scale: [1, 1.05, 1],
              rotate: [0, 1, 0, -1, 0]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          >
            <div className="absolute -inset-6 rounded-full bg-primary/20 blur-xl opacity-70"></div>
            <h1 className="text-4xl md:text-6xl font-orbitron font-bold text-primary neon-text-red relative">
              How ETERNAL Works
            </h1>
          </motion.div>

          <motion.p
            className="text-xl text-contrast/80 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            Understanding the revolutionary <span className="text-accent font-medium">4×2 recursive spillover system</span>
          </motion.p>
        </motion.div>

        {/* Step 1: Join */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.2 }}
          transition={{ duration: 0.7, delay: 0.2 }}
          className="mb-24"
        >
          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2">
              <div className="bg-[#1A1A1A]/90 backdrop-blur-md border border-primary/30 rounded-2xl p-8 shadow-glow relative overflow-hidden">
                {/* Decorative elements */}
                <div className="absolute top-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-primary to-transparent"></div>
                <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-primary to-transparent"></div>
                <div className="absolute top-0 left-0 w-[1px] h-full bg-gradient-to-b from-transparent via-primary/20 to-transparent"></div>
                <div className="absolute top-0 right-0 w-[1px] h-full bg-gradient-to-b from-transparent via-primary/20 to-transparent"></div>

                <motion.div
                  className="absolute -top-6 -left-6 bg-primary text-white text-2xl font-orbitron font-bold w-16 h-16 rounded-full flex items-center justify-center shadow-glow z-10"
                  animate={{
                    boxShadow: ["0 0 10px rgba(159, 0, 0, 0.5)", "0 0 20px rgba(159, 0, 0, 0.8)", "0 0 10px rgba(159, 0, 0, 0.5)"]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  1
                </motion.div>

                <motion.h2
                  className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-6 ml-4"
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-red-500 to-red-700">
                    Join With $6
                  </span>
                </motion.h2>

                <motion.p
                  className="text-contrast/90 mb-6"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  The journey begins with a simple <span className="text-accent font-medium">$6 payment</span> to join the ETERNAL Income Matrix System. This one-time fee is distributed as follows:
                </motion.p>

                <div className="space-y-4 mb-6">
                  <motion.div
                    className="flex items-start bg-background/20 p-4 rounded-lg border border-secondary/20 relative overflow-hidden group"
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                    whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.3)" }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="w-10 h-10 mr-4 flex items-center justify-center bg-primary/20 rounded-full text-accent">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                      </svg>
                    </div>
                    <div className="flex-1 relative z-10">
                      <h3 className="text-accent font-medium mb-1">Direct Referrer</h3>
                      <p className="text-contrast/90">$2 goes to your direct referrer (the person who invited you)</p>
                    </div>
                  </motion.div>

                  <motion.div
                    className="flex items-start bg-background/20 p-4 rounded-lg border border-secondary/20 relative overflow-hidden group"
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                    whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.3)" }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="w-10 h-10 mr-4 flex items-center justify-center bg-primary/20 rounded-full text-accent">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                    <div className="flex-1 relative z-10">
                      <h3 className="text-accent font-medium mb-1">Spillover Receiver</h3>
                      <p className="text-contrast/90">$2 goes to the spillover receiver (the person under whom you're placed)</p>
                    </div>
                  </motion.div>

                  <motion.div
                    className="flex items-start bg-background/20 p-4 rounded-lg border border-secondary/20 relative overflow-hidden group"
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                    whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.3)" }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="w-10 h-10 mr-4 flex items-center justify-center bg-primary/20 rounded-full text-accent">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="flex-1 relative z-10">
                      <h3 className="text-accent font-medium mb-1">Eternal Weekly Fund</h3>
                      <p className="text-contrast/90">$2 goes to the Eternal Weekly Fund (distributed to active members)</p>
                    </div>
                  </motion.div>
                </div>

                <motion.p
                  className="text-contrast/90 bg-primary/10 p-4 rounded-lg border border-primary/20"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                >
                  This fair distribution ensures that everyone in the system benefits from new members joining.
                </motion.p>
              </div>
            </div>

            <div className="md:w-1/2">
              <motion.div
                className="bg-[#1A1A1A]/80 backdrop-blur-md border border-secondary/30 rounded-2xl p-6 shadow-sm relative overflow-hidden"
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7, delay: 0.4 }}
              >
                <div className="absolute top-0 right-0 w-32 h-32 bg-primary/10 rounded-full -mr-16 -mt-16 z-0"></div>

                <motion.h3
                  className="text-2xl font-orbitron font-bold text-white mb-6 flex items-center"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Joining Fee Distribution
                </motion.h3>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  <div className="bg-background/30 p-4 rounded-xl border border-secondary/20 mb-6">
                    <p className="text-accent font-medium mb-2">Joining Fee Per Member:</p>
                    <p className="text-3xl font-orbitron text-white">$6 USD</p>
                  </div>

                  <p className="text-contrast/80 mb-4">
                    This $6 will be distributed in three parts:
                  </p>
                </motion.div>

                <motion.div
                  className="overflow-x-auto bg-background/20 rounded-xl border border-secondary/20"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.7 }}
                >
                  <table className="w-full text-left">
                    <thead>
                      <tr className="border-b border-secondary/30">
                        <th className="py-3 px-4 text-accent">Category</th>
                        <th className="py-3 px-4 text-accent">Amount</th>
                        <th className="py-3 px-4 text-accent">Recipient</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b border-secondary/30">
                        <td className="py-3 px-4 text-contrast">Direct Referral Income</td>
                        <td className="py-3 px-4 text-white font-medium">$2</td>
                        <td className="py-3 px-4 text-contrast">Member who referred you</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-3 px-4 text-contrast">Spillover Income</td>
                        <td className="py-3 px-4 text-white font-medium">$2</td>
                        <td className="py-3 px-4 text-contrast">Member under whom you're placed</td>
                      </tr>
                      <tr>
                        <td className="py-3 px-4 text-contrast">Eternal Weekly Fund</td>
                        <td className="py-3 px-4 text-white font-medium">$2</td>
                        <td className="py-3 px-4 text-contrast">Added to weekly income fund</td>
                      </tr>
                    </tbody>
                  </table>
                </motion.div>
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Step 2: Matrix Structure */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mb-20"
        >
          <div className="flex flex-col md:flex-row-reverse items-center gap-12">
            <div className="md:w-1/2">
              <div className="bg-secondary/20 backdrop-blur-sm border border-secondary/30 rounded-lg p-8 relative neon-border">
                <div className="absolute -top-5 -left-5 bg-primary text-white text-2xl font-orbitron font-bold w-12 h-12 rounded-full flex items-center justify-center">
                  2
                </div>
                <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-6">
                  4×2 Matrix Structure
                </h2>
                <p className="text-contrast/90 mb-4">
                  The ETERNAL system uses a 4×2 matrix structure with recursive spillover:
                </p>
                <ul className="space-y-2 mb-6">
                  <li className="flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span className="text-contrast/90">Each member can directly refer up to 4 people in a set</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span className="text-contrast/90">When you refer more than 4 people, a new set is created</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span className="text-contrast/90">For every 4 referrals, 2 stay under you and 2 spill over to your upline</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span className="text-contrast/90">This recursive spillover continues throughout the system</span>
                  </li>
                </ul>
                <p className="text-contrast/90">
                  This structure ensures that everyone in the system benefits from the growth of their team, creating a powerful network effect.
                </p>
              </div>
            </div>
            <div className="md:w-1/2">
              <div className="bg-background border border-secondary/30 rounded-lg p-6">
                <h3 className="text-xl font-orbitron font-bold text-white mb-4">Matrix Structure</h3>
                <p className="text-contrast/80 mb-4">
                  4×2 Recursive Spillover System
                </p>
                <div className="mb-4">
                  <h4 className="text-lg font-medium text-accent mb-2">How Does Spillover Work?</h4>
                  <p className="text-contrast/80 mb-2">Example:</p>
                  <ul className="space-y-2 text-contrast/80">
                    <li>A → refers B</li>
                    <li>B → refers 4 people: C, D, E, F</li>
                    <li>Out of these 4 people, B keeps 2 (randomly or by left-right rule)</li>
                    <li>The other 2 people go to A (B's upline) as Spillover</li>
                  </ul>
                </div>
                <div>
                  <h4 className="text-lg font-medium text-accent mb-2">Rules of Recursive Spillover:</h4>
                  <ul className="space-y-2 text-contrast/80">
                    <li>For every 4 referrals, 2 stay with you and 2 go to your upline</li>
                    <li>Those who have been spilled over will also have their future referrals spill over to higher levels</li>
                    <li>This way, the work of lower members creates income for upper members</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Step 3: Level System */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mb-20"
        >
          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2">
              <div className="bg-secondary/20 backdrop-blur-sm border border-secondary/30 rounded-lg p-8 relative neon-border">
                <div className="absolute -top-5 -left-5 bg-primary text-white text-2xl font-orbitron font-bold w-12 h-12 rounded-full flex items-center justify-center">
                  3
                </div>
                <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-6">
                  Level System
                </h2>
                <p className="text-contrast/90 mb-4">
                  Your level in the ETERNAL system is determined by the number of direct referrals you have:
                </p>
                <ul className="space-y-2 mb-6">
                  <li className="flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span className="text-contrast/90">Each level requires twice as many referrals as the previous level</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span className="text-contrast/90">Higher levels receive a larger share of the weekly fund</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span className="text-contrast/90">There are 10 levels in total, with Level 10 being the highest</span>
                  </li>
                </ul>
                <p className="text-contrast/90">
                  This level system incentivizes members to continue growing their team, creating a sustainable ecosystem.
                </p>
              </div>
            </div>
            <div className="md:w-1/2">
              <div className="bg-background border border-secondary/30 rounded-lg p-6">
                <h3 className="text-xl font-orbitron font-bold text-white mb-4">Level System</h3>
                <p className="text-contrast/80 mb-4">
                  Your Level is determined by your total number of Direct Referrals.
                </p>
                <div className="overflow-x-auto">
                  <table className="w-full text-left">
                    <thead>
                      <tr className="border-b border-secondary/30">
                        <th className="py-2 px-4 text-accent">Level</th>
                        <th className="py-2 px-4 text-accent">Direct Referrals Needed</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L1</td>
                        <td className="py-2 px-4 text-contrast">4</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L2</td>
                        <td className="py-2 px-4 text-contrast">8</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L3</td>
                        <td className="py-2 px-4 text-contrast">16</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L4</td>
                        <td className="py-2 px-4 text-contrast">32</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L5</td>
                        <td className="py-2 px-4 text-contrast">64</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L6</td>
                        <td className="py-2 px-4 text-contrast">128</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L7</td>
                        <td className="py-2 px-4 text-contrast">256</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L8</td>
                        <td className="py-2 px-4 text-contrast">512</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L9</td>
                        <td className="py-2 px-4 text-contrast">1024</td>
                      </tr>
                      <tr>
                        <td className="py-2 px-4 text-contrast">L10</td>
                        <td className="py-2 px-4 text-contrast">2048</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <p className="text-contrast/80 mt-4">
                  Each level requires twice as many referrals as the previous level
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Step 4: Weekly Income */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.8 }}
          className="mb-20"
        >
          <div className="flex flex-col md:flex-row-reverse items-center gap-12">
            <div className="md:w-1/2">
              <div className="bg-secondary/20 backdrop-blur-sm border border-secondary/30 rounded-lg p-8 relative neon-border">
                <div className="absolute -top-5 -left-5 bg-primary text-white text-2xl font-orbitron font-bold w-12 h-12 rounded-full flex items-center justify-center">
                  4
                </div>
                <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-6">
                  Weekly Income
                </h2>
                <p className="text-contrast/90 mb-4">
                  The Eternal Weekly Fund is distributed every Sunday to active members:
                </p>
                <ul className="space-y-2 mb-6">
                  <li className="flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span className="text-contrast/90">$2 from every new join goes into the Eternal Fund</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span className="text-contrast/90">To be considered active, you must refer at least 1 person that week</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span className="text-contrast/90">Your share of the fund depends on your level</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span className="text-contrast/90">Higher levels receive a larger percentage of the fund</span>
                  </li>
                </ul>
                <p className="text-contrast/90">
                  This weekly distribution creates a consistent passive income stream for active members.
                </p>
              </div>
            </div>
            <div className="md:w-1/2">
              <div className="bg-background border border-secondary/30 rounded-lg p-6">
                <h3 className="text-xl font-orbitron font-bold text-white mb-4">Weekly Income</h3>
                <p className="text-contrast/80 mb-4">
                  $2 from each join is added to the Eternal Income Fund
                </p>
                <p className="text-contrast/80 mb-4">
                  Every Sunday, the accumulated funds are distributed among Active members
                </p>
                <div className="mb-4">
                  <h4 className="text-lg font-medium text-accent mb-2">Who is an Active Member?</h4>
                  <p className="text-contrast/80">
                    Those who have referred at least 1 person that week
                  </p>
                </div>
                <div>
                  <h4 className="text-lg font-medium text-accent mb-2">Weekly Income Distribution by Level:</h4>
                  <div className="overflow-x-auto">
                    <table className="w-full text-left">
                      <thead>
                        <tr className="border-b border-secondary/30">
                          <th className="py-2 px-4 text-accent">Level</th>
                          <th className="py-2 px-4 text-accent">Distribution Share</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-b border-secondary/30">
                          <td className="py-2 px-4 text-contrast">Level 1</td>
                          <td className="py-2 px-4 text-contrast">10%</td>
                        </tr>
                        <tr className="border-b border-secondary/30">
                          <td className="py-2 px-4 text-contrast">Level 2</td>
                          <td className="py-2 px-4 text-contrast">20%</td>
                        </tr>
                        <tr className="border-b border-secondary/30">
                          <td className="py-2 px-4 text-contrast">Level 3</td>
                          <td className="py-2 px-4 text-contrast">30%</td>
                        </tr>
                        <tr className="border-b border-secondary/30">
                          <td className="py-2 px-4 text-contrast">Level 4</td>
                          <td className="py-2 px-4 text-contrast">40%</td>
                        </tr>
                        <tr className="border-b border-secondary/30">
                          <td className="py-2 px-4 text-contrast">Level 5</td>
                          <td className="py-2 px-4 text-contrast">50%</td>
                        </tr>
                        <tr className="border-b border-secondary/30">
                          <td className="py-2 px-4 text-contrast">Level 6</td>
                          <td className="py-2 px-4 text-contrast">60%</td>
                        </tr>
                        <tr className="border-b border-secondary/30">
                          <td className="py-2 px-4 text-contrast">Level 7</td>
                          <td className="py-2 px-4 text-contrast">70%</td>
                        </tr>
                        <tr className="border-b border-secondary/30">
                          <td className="py-2 px-4 text-contrast">Level 8</td>
                          <td className="py-2 px-4 text-contrast">80%</td>
                        </tr>
                        <tr className="border-b border-secondary/30">
                          <td className="py-2 px-4 text-contrast">Level 9</td>
                          <td className="py-2 px-4 text-contrast">90%</td>
                        </tr>
                        <tr>
                          <td className="py-2 px-4 text-contrast">Level 10</td>
                          <td className="py-2 px-4 text-contrast">100%</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7 }}
          className="text-center mt-16"
        >
          <div className="bg-[#1A1A1A]/90 backdrop-blur-md border border-primary/30 rounded-2xl p-12 shadow-glow relative overflow-hidden">
            {/* Decorative elements */}
            <div className="absolute top-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-primary to-transparent"></div>
            <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-primary to-transparent"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-transparent to-primary/10 opacity-20"></div>

            <motion.h2
              className="text-3xl md:text-4xl font-orbitron font-bold text-accent mb-6"
              initial={{ opacity: 0, y: -10 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-red-500 to-red-700">
                Ready to Start Your ETERNAL Journey?
              </span>
            </motion.h2>

            <motion.p
              className="text-xl text-contrast/90 max-w-3xl mx-auto mb-10"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              Join now with just <span className="text-accent font-medium">$6</span> and become part of the revolutionary income system that's changing lives.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.6 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="relative inline-block"
            >
              <div className="absolute -inset-1 bg-gradient-to-r from-primary/30 to-primary/50 rounded-md blur-sm"></div>
              <Link
                to="/connect-wallet"
                className="relative px-10 py-4 bg-primary text-white font-orbitron font-bold rounded-md hover:bg-primary/80 transition-all duration-300 shadow-glow border border-primary/50 text-lg inline-flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Connect Wallet & Join
              </Link>
            </motion.div>

            {/* Animated particles */}
            <div className="absolute inset-0 z-0 overflow-hidden pointer-events-none">
              {[...Array(10)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 bg-primary rounded-full shadow-glow"
                  initial={{
                    x: Math.random() * 100 + "%",
                    y: Math.random() * 100 + "%",
                    opacity: Math.random() * 0.5 + 0.3
                  }}
                  animate={{
                    y: [null, Math.random() * 100 + "%"],
                    opacity: [null, Math.random() * 0.3 + 0.1]
                  }}
                  transition={{
                    duration: Math.random() * 5 + 5,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                />
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default HowItWorks
