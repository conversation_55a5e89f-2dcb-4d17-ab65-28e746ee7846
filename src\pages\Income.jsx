import React, { useState } from 'react';
import { motion } from 'framer-motion';

// Mock Data
import mockUserData from '../data/mockUserData';

const Income = () => {
  const [activeTab, setActiveTab] = useState('weekly');
  const [selectedPeriod, setSelectedPeriod] = useState('all');

  // Sample income data
  const incomeData = {
    weekly: [
      { week: 'Week 1', direct: 25, spillover: 15, matrix: 10, total: 50, date: '2023-06-25' },
      { week: 'Week 2', direct: 30, spillover: 20, matrix: 15, total: 65, date: '2023-07-02' },
      { week: 'Week 3', direct: 40, spillover: 25, matrix: 20, total: 85, date: '2023-07-09' },
      { week: 'Week 4', direct: 35, spillover: 30, matrix: 25, total: 90, date: '2023-07-16' },
      { week: 'Week 5', direct: 45, spillover: 35, matrix: 30, total: 110, date: '2023-07-23' },
      { week: 'Week 6', direct: 50, spillover: 40, matrix: 35, total: 125, date: '2023-07-30' },
      { week: 'Week 7', direct: 55, spillover: 45, matrix: 40, total: 140, date: '2023-08-06' },
      { week: 'Week 8', direct: 60, spillover: 50, matrix: 45, total: 155, date: '2023-08-13' },
    ],
    monthly: [
      { month: 'Jan', direct: 100, spillover: 80, matrix: 70, total: 250, date: '2023-01-31' },
      { month: 'Feb', direct: 120, spillover: 90, matrix: 80, total: 290, date: '2023-02-28' },
      { month: 'Mar', direct: 140, spillover: 100, matrix: 90, total: 330, date: '2023-03-31' },
      { month: 'Apr', direct: 160, spillover: 110, matrix: 100, total: 370, date: '2023-04-30' },
      { month: 'May', direct: 180, spillover: 120, matrix: 110, total: 410, date: '2023-05-31' },
      { month: 'Jun', direct: 200, spillover: 130, matrix: 120, total: 450, date: '2023-06-30' },
      { month: 'Jul', direct: 220, spillover: 140, matrix: 130, total: 490, date: '2023-07-31' },
      { month: 'Aug', direct: 240, spillover: 150, matrix: 140, total: 530, date: '2023-08-13' },
    ]
  };

  // Filter data based on selected period
  const getFilteredData = () => {
    const data = activeTab === 'weekly' ? incomeData.weekly : incomeData.monthly;

    if (selectedPeriod === 'all') {
      return data;
    } else if (selectedPeriod === 'last3') {
      return data.slice(-3);
    } else if (selectedPeriod === 'last6') {
      return data.slice(-6);
    }

    return data;
  };

  const filteredData = getFilteredData();

  // Calculate totals
  const totalDirect = filteredData.reduce((sum, item) => sum + item.direct, 0);
  const totalSpillover = filteredData.reduce((sum, item) => sum + item.spillover, 0);
  const totalMatrix = filteredData.reduce((sum, item) => sum + item.matrix, 0);
  const totalIncome = totalDirect + totalSpillover + totalMatrix;

  // Format currency
  const formatCurrency = (value) => {
    return `$${value.toFixed(2)}`;
  };

  return (
    <div className="flex flex-col min-h-screen bg-background text-white">
      {/* Background effects */}
      <div className="fixed inset-0 z-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-black to-background opacity-90"></div>
        {/* Animated glow effects */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full bg-primary/5 filter blur-[100px] animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full bg-primary/5 filter blur-[100px] animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>


      {/* Main Content */}
      <div className="flex-1 flex flex-col relative z-10 w-full">
        <div className="flex-1 p-4 md:p-6 overflow-x-hidden max-w-7xl mx-auto w-full">
          <div className="md:grid md:grid-cols-12 md:gap-6 space-y-6 md:space-y-0">
            {/* Main Content Column */}
            <div className="md:col-span-8 space-y-6">
              {/* Page Header */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <h1 className="text-2xl font-bold text-white mb-2">Income Analytics</h1>
                <p className="text-white/70">Track and analyze your earnings from all sources</p>
              </motion.div>

              {/* Income Summary */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <h2 className="text-sm text-white/70 mb-3 flex items-center">
                  <span className="w-1 h-4 bg-primary mr-2"></span>
                  Income Summary
                </h2>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                  <div className="bg-background rounded-lg p-3 border border-primary/20">
                    <div className="text-white/70 text-xs mb-1">Total Earnings</div>
                    <div className="text-white text-xl font-bold">{formatCurrency(totalIncome)}</div>
                    <div className="text-white/50 text-[10px] mt-1">All time</div>
                  </div>

                  <div className="bg-background rounded-lg p-3 border border-primary/20">
                    <div className="text-white/70 text-xs mb-1">Direct Referrals</div>
                    <div className="text-white text-xl font-bold">{formatCurrency(totalDirect)}</div>
                    <div className="text-white/50 text-[10px] mt-1">{Math.round((totalDirect/totalIncome)*100)}% of total</div>
                  </div>

                  <div className="bg-background rounded-lg p-3 border border-primary/20">
                    <div className="text-white/70 text-xs mb-1">Spillover</div>
                    <div className="text-white text-xl font-bold">{formatCurrency(totalSpillover)}</div>
                    <div className="text-white/50 text-[10px] mt-1">{Math.round((totalSpillover/totalIncome)*100)}% of total</div>
                  </div>

                  <div className="bg-background rounded-lg p-3 border border-primary/20">
                    <div className="text-white/70 text-xs mb-1">Matrix Payouts</div>
                    <div className="text-white text-xl font-bold">{formatCurrency(totalMatrix)}</div>
                    <div className="text-white/50 text-[10px] mt-1">{Math.round((totalMatrix/totalIncome)*100)}% of total</div>
                  </div>
                </div>
              </motion.div>

              {/* Income Chart */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-sm text-white/70 flex items-center">
                    <span className="w-1 h-4 bg-primary mr-2"></span>
                    Income History
                  </h2>

                  <div className="flex space-x-2">
                    <select
                      className="bg-background text-white text-xs border border-primary/20 rounded px-2 py-1"
                      value={selectedPeriod}
                      onChange={(e) => setSelectedPeriod(e.target.value)}
                    >
                      <option value="all">All Time</option>
                      <option value="last3">Last 3 Periods</option>
                      <option value="last6">Last 6 Periods</option>
                    </select>

                    <div className="flex border border-primary/20 rounded overflow-hidden">
                      <button
                        className={`px-2 py-1 text-xs ${activeTab === 'weekly' ? 'bg-primary text-white' : 'bg-background text-white/70'}`}
                        onClick={() => setActiveTab('weekly')}
                      >
                        Weekly
                      </button>
                      <button
                        className={`px-2 py-1 text-xs ${activeTab === 'monthly' ? 'bg-primary text-white' : 'bg-background text-white/70'}`}
                        onClick={() => setActiveTab('monthly')}
                      >
                        Monthly
                      </button>
                    </div>
                  </div>
                </div>

                {/* Chart Legend */}
                <div className="flex items-center mb-3">
                  <div className="flex items-center mr-4">
                    <div className="w-3 h-3 bg-chart-purple rounded-sm mr-1"></div>
                    <span className="text-white/70 text-xs">Direct</span>
                  </div>
                  <div className="flex items-center mr-4">
                    <div className="w-3 h-3 bg-chart-blue rounded-sm mr-1"></div>
                    <span className="text-white/70 text-xs">Spillover</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-chart-green rounded-sm mr-1"></div>
                    <span className="text-white/70 text-xs">Matrix</span>
                  </div>
                </div>

                {/* Chart */}
                <div className="bg-background rounded-lg p-4 border border-primary/20 h-64 flex items-end justify-between">
                  {filteredData.map((item, index) => (
                    <div key={index} className="flex flex-col items-center w-full">
                      <div className="w-full flex items-end justify-center h-48 space-x-0.5 md:space-x-1">
                        <div className="w-2 md:w-3 bg-chart-purple rounded-t" style={{ height: `${(item.direct / 250) * 100}%` }}></div>
                        <div className="w-2 md:w-3 bg-chart-blue rounded-t" style={{ height: `${(item.spillover / 250) * 100}%` }}></div>
                        <div className="w-2 md:w-3 bg-chart-green rounded-t" style={{ height: `${(item.matrix / 250) * 100}%` }}></div>
                      </div>
                      <div className="text-white/50 text-[10px] md:text-xs mt-1">
                        {activeTab === 'weekly' ? `W${index + 1}` : item.month}
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            </div>

            {/* Sidebar Column */}
            <div className="md:col-span-4 space-y-6">
              {/* Next Distribution */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <h2 className="text-sm text-white/70 mb-3 flex items-center">
                  <span className="w-1 h-4 bg-primary mr-2"></span>
                  Next Distribution
                </h2>

                <div className="bg-background rounded-lg p-3 border border-primary/20 mb-3">
                  <div className="text-center">
                    <div className="text-white/70 text-xs mb-2">Time Remaining</div>
                    <div className="flex justify-center space-x-2">
                      <div className="bg-secondary w-12 p-2 rounded-lg border border-primary/20">
                        <div className="text-white text-xl font-bold">6</div>
                        <div className="text-white/50 text-[10px]">Days</div>
                      </div>
                      <div className="bg-secondary w-12 p-2 rounded-lg border border-primary/20">
                        <div className="text-white text-xl font-bold">17</div>
                        <div className="text-white/50 text-[10px]">Hours</div>
                      </div>
                      <div className="bg-secondary w-12 p-2 rounded-lg border border-primary/20">
                        <div className="text-white text-xl font-bold">9</div>
                        <div className="text-white/50 text-[10px]">Mins</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-between items-center mb-2">
                  <div className="text-white/70 text-xs">Estimated Pool Size:</div>
                  <div className="text-white text-sm font-bold">$1,245.00</div>
                </div>

                <div className="flex justify-between items-center mb-2">
                  <div className="text-white/70 text-xs">Your Estimated Share:</div>
                  <div className="text-highlight text-sm font-bold">$42.56</div>
                </div>

                <div className="flex justify-between items-center">
                  <div className="text-white/70 text-xs">Active Members:</div>
                  <div className="text-white text-sm font-bold">28</div>
                </div>
              </motion.div>

              {/* Income Sources */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <h2 className="text-sm text-white/70 mb-3 flex items-center">
                  <span className="w-1 h-4 bg-primary mr-2"></span>
                  Income Sources
                </h2>

                <div className="space-y-3">
                  <div className="bg-background rounded-lg p-3 border border-primary/20">
                    <div className="flex justify-between items-center mb-1">
                      <div className="text-white text-sm">Direct Referrals</div>
                      <div className="text-white text-sm font-bold">{Math.round((totalDirect/totalIncome)*100)}%</div>
                    </div>
                    <div className="h-2 bg-secondary rounded-full overflow-hidden">
                      <div className="h-full bg-chart-purple" style={{ width: `${(totalDirect/totalIncome)*100}%` }}></div>
                    </div>
                  </div>

                  <div className="bg-background rounded-lg p-3 border border-primary/20">
                    <div className="flex justify-between items-center mb-1">
                      <div className="text-white text-sm">Spillover Bonuses</div>
                      <div className="text-white text-sm font-bold">{Math.round((totalSpillover/totalIncome)*100)}%</div>
                    </div>
                    <div className="h-2 bg-secondary rounded-full overflow-hidden">
                      <div className="h-full bg-chart-blue" style={{ width: `${(totalSpillover/totalIncome)*100}%` }}></div>
                    </div>
                  </div>

                  <div className="bg-background rounded-lg p-3 border border-primary/20">
                    <div className="flex justify-between items-center mb-1">
                      <div className="text-white text-sm">Matrix Payouts</div>
                      <div className="text-white text-sm font-bold">{Math.round((totalMatrix/totalIncome)*100)}%</div>
                    </div>
                    <div className="h-2 bg-secondary rounded-full overflow-hidden">
                      <div className="h-full bg-chart-green" style={{ width: `${(totalMatrix/totalIncome)*100}%` }}></div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Income;
