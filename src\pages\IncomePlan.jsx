import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'

const IncomePlan = () => {
  return (
    <div className="py-16 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-orbitron font-bold text-primary mb-6">Income Plan</h1>
          <p className="text-xl text-contrast/80 max-w-3xl mx-auto">
            Detailed breakdown of the ETERNAL Income Matrix System
          </p>
        </motion.div>

        {/* Join Fee Breakdown */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mb-20"
        >
          <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-8 text-center">
            Join <PERSON>e & Breakdown
          </h2>

          <div className="bg-secondary/20 backdrop-blur-sm border border-secondary/30 rounded-lg p-8 neon-border">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-orbitron font-bold text-white mb-4">Join Fee: $6 USD</h3>
                <p className="text-contrast/90 mb-6">
                  Your one-time $6 joining fee is distributed in three ways to create a balanced ecosystem:
                </p>
                <ul className="space-y-4">
                  <li className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-white font-bold">1</span>
                    </div>
                    <div>
                      <h4 className="text-lg font-medium text-accent">Direct Referral Income</h4>
                      <p className="text-contrast/80">$2 goes to the person who referred you</p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-white font-bold">2</span>
                    </div>
                    <div>
                      <h4 className="text-lg font-medium text-accent">Spillover Income</h4>
                      <p className="text-contrast/80">$2 goes to the person under whom you're placed</p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-white font-bold">3</span>
                    </div>
                    <div>
                      <h4 className="text-lg font-medium text-accent">Eternal Weekly Fund</h4>
                      <p className="text-contrast/80">$2 goes to the weekly income fund</p>
                    </div>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-orbitron font-bold text-white mb-4">Joining Fee Distribution</h3>
                <p className="text-contrast/90 mb-4">
                  Joining Fee Per Member: $6 USD
                </p>
                <p className="text-contrast/90 mb-2">
                  This $6 will be distributed in three parts:
                </p>
                <div className="overflow-x-auto">
                  <table className="w-full text-left">
                    <thead>
                      <tr className="border-b border-secondary/30">
                        <th className="py-2 px-4 text-accent">Category</th>
                        <th className="py-2 px-4 text-accent">Amount</th>
                        <th className="py-2 px-4 text-accent">Recipient</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">Direct Referral Income</td>
                        <td className="py-2 px-4 text-contrast">$2</td>
                        <td className="py-2 px-4 text-contrast">Member who referred you</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">Spillover Income</td>
                        <td className="py-2 px-4 text-contrast">$2</td>
                        <td className="py-2 px-4 text-contrast">Member under whom you're placed</td>
                      </tr>
                      <tr>
                        <td className="py-2 px-4 text-contrast">Eternal Weekly Fund</td>
                        <td className="py-2 px-4 text-contrast">$2</td>
                        <td className="py-2 px-4 text-contrast">Added to weekly income fund</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Matrix Structure */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mb-20"
        >
          <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-8 text-center">
            Matrix Structure (4×2 Recursive Spillover)
          </h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-secondary/20 backdrop-blur-sm border border-secondary/30 rounded-lg p-8 neon-border">
              <h3 className="text-xl font-orbitron font-bold text-white mb-4">How The Matrix Works</h3>
              <p className="text-contrast/90 mb-6">
                The ETERNAL system uses a 4×2 matrix structure with recursive spillover technology:
              </p>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <span className="text-accent mr-2">•</span>
                  <span className="text-contrast/90">Each member can directly refer unlimited people</span>
                </li>
                <li className="flex items-start">
                  <span className="text-accent mr-2">•</span>
                  <span className="text-contrast/90">For every 4 referrals, a new set is created</span>
                </li>
                <li className="flex items-start">
                  <span className="text-accent mr-2">•</span>
                  <span className="text-contrast/90">In each set of 4 referrals, 2 stay under you and 2 spill over to your upline</span>
                </li>
                <li className="flex items-start">
                  <span className="text-accent mr-2">•</span>
                  <span className="text-contrast/90">Spillover happens automatically based on the system's algorithm</span>
                </li>
                <li className="flex items-start">
                  <span className="text-accent mr-2">•</span>
                  <span className="text-contrast/90">Members who receive spillovers also generate spillovers for their upline</span>
                </li>
              </ul>
            </div>

            <div className="bg-secondary/20 backdrop-blur-sm border border-secondary/30 rounded-lg p-8 neon-border">
              <h3 className="text-xl font-orbitron font-bold text-white mb-4">Matrix Structure</h3>
              <p className="text-contrast/90 mb-4">
                The foundation of this plan:
              </p>
              <ul className="space-y-2 text-contrast/90 mb-6">
                <li>Each member can directly refer up to 4 people as one set</li>
                <li>But you can refer unlimited members - for every 4 referrals, a new set is created</li>
                <li>Spillover happens automatically</li>
              </ul>

              <h4 className="text-lg font-medium text-accent mb-2">How Does Spillover Work?</h4>
              <p className="text-contrast/90 mb-2">Example:</p>
              <ul className="space-y-2 text-contrast/90 mb-6">
                <li>A → refers B</li>
                <li>B → refers 4 people: C, D, E, F</li>
                <li>Out of these 4 people, B keeps 2 (randomly or by left-right rule)</li>
                <li>The other 2 people go to A (B's upline) as Spillover</li>
              </ul>

              <h4 className="text-lg font-medium text-accent mb-2">Rules of Recursive Spillover:</h4>
              <ul className="space-y-2 text-contrast/90">
                <li>For every 4 referrals, 2 stay with you and 2 go to your upline</li>
                <li>Those who have been spilled over will also have their future referrals spill over to higher levels</li>
                <li>This way, the work of lower members creates income for upper members</li>
              </ul>
            </div>
          </div>
        </motion.div>

        {/* Level System */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mb-20"
        >
          <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-8 text-center">
            Level System
          </h2>

          <div className="bg-secondary/20 backdrop-blur-sm border border-secondary/30 rounded-lg p-8 neon-border">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-orbitron font-bold text-white mb-4">Level Advancement</h3>
                <p className="text-contrast/90 mb-6">
                  Your level in the ETERNAL system is determined by the number of direct referrals you have:
                </p>
                <ul className="space-y-2 text-contrast/90 mb-6">
                  <li>Each level requires twice as many referrals as the previous level</li>
                  <li>Higher levels receive a larger share of the weekly fund</li>
                  <li>There are 10 levels in total, with Level 10 being the highest</li>
                </ul>
                <p className="text-contrast/90">
                  This level system incentivizes members to continue growing their team, creating a sustainable ecosystem.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-orbitron font-bold text-white mb-4">Level System</h3>
                <p className="text-contrast/90 mb-4">
                  Your Level is determined by your total number of Direct Referrals.
                </p>
                <div className="overflow-x-auto">
                  <table className="w-full text-left">
                    <thead>
                      <tr className="border-b border-secondary/30">
                        <th className="py-2 px-4 text-accent">Level</th>
                        <th className="py-2 px-4 text-accent">Direct Referrals Needed</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L1</td>
                        <td className="py-2 px-4 text-contrast">4</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L2</td>
                        <td className="py-2 px-4 text-contrast">8</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L3</td>
                        <td className="py-2 px-4 text-contrast">16</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L4</td>
                        <td className="py-2 px-4 text-contrast">32</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L5</td>
                        <td className="py-2 px-4 text-contrast">64</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L6</td>
                        <td className="py-2 px-4 text-contrast">128</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L7</td>
                        <td className="py-2 px-4 text-contrast">256</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L8</td>
                        <td className="py-2 px-4 text-contrast">512</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">L9</td>
                        <td className="py-2 px-4 text-contrast">1024</td>
                      </tr>
                      <tr>
                        <td className="py-2 px-4 text-contrast">L10</td>
                        <td className="py-2 px-4 text-contrast">2048</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <p className="text-contrast/90 mt-4">
                  Each level requires twice as many referrals as the previous level
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Weekly Income */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.8 }}
          className="mb-20"
        >
          <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-8 text-center">
            Weekly Income (Eternal Fund)
          </h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-secondary/20 backdrop-blur-sm border border-secondary/30 rounded-lg p-8 neon-border">
              <h3 className="text-xl font-orbitron font-bold text-white mb-4">Weekly Distribution</h3>
              <p className="text-contrast/90 mb-6">
                The Eternal Weekly Fund is distributed every Sunday to active members:
              </p>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <span className="text-accent mr-2">•</span>
                  <span className="text-contrast/90">$2 from every new join goes into the Eternal Fund</span>
                </li>
                <li className="flex items-start">
                  <span className="text-accent mr-2">•</span>
                  <span className="text-contrast/90">To be considered active, you must refer at least 1 person that week</span>
                </li>
                <li className="flex items-start">
                  <span className="text-accent mr-2">•</span>
                  <span className="text-contrast/90">Your share of the fund depends on your level</span>
                </li>
                <li className="flex items-start">
                  <span className="text-accent mr-2">•</span>
                  <span className="text-contrast/90">Higher levels receive a larger percentage of the fund</span>
                </li>
              </ul>
              <p className="text-contrast/90 mt-6">
                For example, if the weekly fund is $10,000 and you're at Level 3, you'll receive a share of the 30% portion allocated to Level 3 members.
              </p>
            </div>

            <div className="bg-secondary/20 backdrop-blur-sm border border-secondary/30 rounded-lg p-8 neon-border">
              <h3 className="text-xl font-orbitron font-bold text-white mb-4">Weekly Income Distribution</h3>
              <p className="text-contrast/90 mb-4">
                $2 from each join is added to the Eternal Income Fund
              </p>
              <p className="text-contrast/90 mb-4">
                Every Sunday, the accumulated funds are distributed among Active members
              </p>
              <div className="mb-4">
                <h4 className="text-lg font-medium text-accent mb-2">Who is an Active Member?</h4>
                <p className="text-contrast/90">
                  Those who have referred at least 1 person that week
                </p>
              </div>
              <div>
                <h4 className="text-lg font-medium text-accent mb-2">Weekly Income Distribution by Level:</h4>
                <div className="overflow-x-auto">
                  <table className="w-full text-left">
                    <thead>
                      <tr className="border-b border-secondary/30">
                        <th className="py-2 px-4 text-accent">Level</th>
                        <th className="py-2 px-4 text-accent">Distribution Share</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">Level 1</td>
                        <td className="py-2 px-4 text-contrast">10%</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">Level 2</td>
                        <td className="py-2 px-4 text-contrast">20%</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">Level 3</td>
                        <td className="py-2 px-4 text-contrast">30%</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">Level 4</td>
                        <td className="py-2 px-4 text-contrast">40%</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">Level 5</td>
                        <td className="py-2 px-4 text-contrast">50%</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">Level 6</td>
                        <td className="py-2 px-4 text-contrast">60%</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">Level 7</td>
                        <td className="py-2 px-4 text-contrast">70%</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">Level 8</td>
                        <td className="py-2 px-4 text-contrast">80%</td>
                      </tr>
                      <tr className="border-b border-secondary/30">
                        <td className="py-2 px-4 text-contrast">Level 9</td>
                        <td className="py-2 px-4 text-contrast">90%</td>
                      </tr>
                      <tr>
                        <td className="py-2 px-4 text-contrast">Level 10</td>
                        <td className="py-2 px-4 text-contrast">100%</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Plan Highlights */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 1 }}
          className="mb-20"
        >
          <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-8 text-center">
            Plan Highlights
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-background border border-secondary/30 rounded-lg p-6">
              <div className="text-primary text-4xl mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-orbitron font-bold text-white mb-2">Auto Income</h3>
              <p className="text-contrast/80">
                Spillover system ensures income even if you're not actively referring.
              </p>
            </div>

            <div className="bg-background border border-secondary/30 rounded-lg p-6">
              <div className="text-primary text-4xl mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-orbitron font-bold text-white mb-2">Team Growth</h3>
              <p className="text-contrast/80">
                Everyone's work contributes to the growth of the entire team.
              </p>
            </div>

            <div className="bg-background border border-secondary/30 rounded-lg p-6">
              <div className="text-primary text-4xl mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-orbitron font-bold text-white mb-2">Weekly Passive Income</h3>
              <p className="text-contrast/80">
                Regular weekly distributions create consistent passive income.
              </p>
            </div>

            <div className="bg-background border border-secondary/30 rounded-lg p-6">
              <div className="text-primary text-4xl mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
              <h3 className="text-xl font-orbitron font-bold text-white mb-2">Incentivized Growth</h3>
              <p className="text-contrast/80">
                Higher levels mean higher rewards, encouraging continuous team building.
              </p>
            </div>
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 1.2 }}
          className="text-center"
        >
          <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-6">
            Ready to Start Your ETERNAL Journey?
          </h2>
          <p className="text-xl text-contrast/80 max-w-3xl mx-auto mb-8">
            Join now with just $6 and become part of the revolutionary income system that's changing lives.
          </p>
          <Link
            to="/connect-wallet"
            className="btn-primary text-lg px-8 py-3"
          >
            Connect Wallet & Join
          </Link>
        </motion.div>
      </div>
    </div>
  )
}

export default IncomePlan
