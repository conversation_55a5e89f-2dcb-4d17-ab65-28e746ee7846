import React, { useState } from 'react';
import { motion } from 'framer-motion';

// Mock Data
import mockUserData from '../data/mockUserData';

const Matrix = () => {
  const [activeMatrix, setActiveMatrix] = useState('x3');

  // Sample matrix data
  const matrixData = {
    x3: {
      name: 'X3 Matrix',
      description: 'Basic 3x1 matrix with 3 positions per level',
      levels: [
        { level: 1, filled: 3, total: 3, earnings: 18.00, status: 'complete' },
        { level: 2, filled: 5, total: 9, earnings: 30.00, status: 'active' },
        { level: 3, filled: 2, total: 27, earnings: 12.00, status: 'active' },
        { level: 4, filled: 0, total: 81, earnings: 0.00, status: 'inactive' },
      ],
      totalEarnings: 60.00,
      partners: [
        { id: 101, name: '<PERSON>', level: 1, position: 1, date: '2023-07-15' },
        { id: 102, name: '<PERSON>', level: 1, position: 2, date: '2023-07-18' },
        { id: 103, name: '<PERSON>', level: 1, position: 3, date: '2023-07-20' },
        { id: 104, name: '<PERSON>', level: 2, position: 1, date: '2023-07-22' },
        { id: 105, name: '<PERSON>', level: 2, position: 2, date: '2023-07-25' },
        { id: 106, name: '<PERSON> <PERSON>', level: 2, position: 3, date: '2023-07-28' },
        { id: 107, name: '<PERSON> <PERSON>', level: 2, position: 4, date: '2023-08-01' },
        { id: 108, name: '<PERSON> <PERSON>', level: 2, position: 5, date: '2023-08-05' },
        { id: 109, name: 'Thomas White', level: 3, position: 1, date: '2023-08-08' },
        { id: 110, name: 'Lisa Harris', level: 3, position: 2, date: '2023-08-10' },
      ]
    },
    x4: {
      name: 'X4 Matrix',
      description: 'Advanced 2x2 matrix with 4 positions per level',
      levels: [
        { level: 1, filled: 4, total: 4, earnings: 24.00, status: 'complete' },
        { level: 2, filled: 6, total: 16, earnings: 36.00, status: 'active' },
        { level: 3, filled: 0, total: 64, earnings: 0.00, status: 'inactive' },
      ],
      totalEarnings: 60.00,
      partners: [
        { id: 201, name: 'Daniel Clark', level: 1, position: 1, date: '2023-07-15' },
        { id: 202, name: 'Michelle Lewis', level: 1, position: 2, date: '2023-07-18' },
        { id: 203, name: 'Christopher Lee', level: 1, position: 3, date: '2023-07-20' },
        { id: 204, name: 'Amanda Walker', level: 1, position: 4, date: '2023-07-22' },
        { id: 205, name: 'Matthew Hall', level: 2, position: 1, date: '2023-07-25' },
        { id: 206, name: 'Olivia Young', level: 2, position: 2, date: '2023-07-28' },
        { id: 207, name: 'Andrew King', level: 2, position: 3, date: '2023-08-01' },
        { id: 208, name: 'Sophia Wright', level: 2, position: 4, date: '2023-08-05' },
        { id: 209, name: 'James Scott', level: 2, position: 5, date: '2023-08-08' },
        { id: 210, name: 'Emma Green', level: 2, position: 6, date: '2023-08-10' },
      ]
    },
    x5: {
      name: 'X5 Matrix',
      description: 'Premium 5x1 matrix with 5 positions per level',
      levels: [
        { level: 1, filled: 3, total: 5, earnings: 18.00, status: 'active' },
        { level: 2, filled: 0, total: 25, earnings: 0.00, status: 'inactive' },
      ],
      totalEarnings: 18.00,
      partners: [
        { id: 301, name: 'William Turner', level: 1, position: 1, date: '2023-08-01' },
        { id: 302, name: 'Ava Martinez', level: 1, position: 2, date: '2023-08-05' },
        { id: 303, name: 'Ethan Robinson', level: 1, position: 3, date: '2023-08-10' },
      ]
    },
    x6: {
      name: 'X6 Matrix',
      description: 'Elite 3x2 matrix with 6 positions per level',
      levels: [
        { level: 1, filled: 2, total: 6, earnings: 12.00, status: 'active' },
        { level: 2, filled: 0, total: 36, earnings: 0.00, status: 'inactive' },
      ],
      totalEarnings: 12.00,
      partners: [
        { id: 401, name: 'Sophia Adams', level: 1, position: 1, date: '2023-08-05' },
        { id: 402, name: 'Noah Campbell', level: 1, position: 2, date: '2023-08-10' },
      ]
    }
  };

  const currentMatrix = matrixData[activeMatrix];

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  return (
    <div className="flex flex-col min-h-screen bg-background text-white">
      {/* Background effects */}
      <div className="fixed inset-0 z-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-black to-background opacity-90"></div>
        {/* Animated glow effects */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full bg-primary/5 filter blur-[100px] animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full bg-primary/5 filter blur-[100px] animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>


      {/* Main Content */}
      <div className="flex-1 flex flex-col relative z-10 w-full">
        <div className="flex-1 p-4 md:p-6 overflow-x-hidden max-w-7xl mx-auto w-full">
          <div className="md:grid md:grid-cols-12 md:gap-6 space-y-6 md:space-y-0">
            {/* Main Content Column */}
            <div className="md:col-span-8 space-y-6">
              {/* Page Header */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <h1 className="text-2xl font-bold text-white mb-2">Matrix System</h1>
                <p className="text-white/70">View and manage your matrix positions and earnings</p>
              </motion.div>

              {/* Matrix Tabs */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <div className="flex space-x-2 mb-4 overflow-x-auto pb-2">
                  {Object.keys(matrixData).map((matrix) => (
                    <button
                      key={matrix}
                      className={`px-4 py-2 rounded-lg text-sm font-medium ${
                        activeMatrix === matrix
                          ? 'bg-primary text-white'
                          : 'bg-background text-white/70 hover:bg-background/80'
                      }`}
                      onClick={() => setActiveMatrix(matrix)}
                    >
                      {matrixData[matrix].name}
                    </button>
                  ))}
                </div>

                <div className="bg-background rounded-lg p-4 border border-primary/20 mb-4">
                  <div className="flex flex-col md:flex-row md:justify-between md:items-center">
                    <div>
                      <h2 className="text-lg font-bold text-white">{currentMatrix.name}</h2>
                      <p className="text-white/70 text-sm">{currentMatrix.description}</p>
                    </div>
                    <div className="mt-3 md:mt-0">
                      <div className="bg-primary/10 text-primary px-3 py-1 rounded-lg text-sm">
                        Total Earnings: ${currentMatrix.totalEarnings.toFixed(2)}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Matrix Visualization */}
                <div className="bg-background rounded-lg p-4 border border-primary/20 mb-4">
                  <h3 className="text-sm font-medium text-white mb-3">Matrix Structure</h3>

                  <div className="flex flex-wrap gap-4 justify-center">
                    {currentMatrix.levels.map((level) => (
                      <div key={level.level} className="text-center">
                        <div className="mb-2 text-white/70 text-xs">Level {level.level}</div>
                        <div className="relative">
                          <svg width="120" height="120" viewBox="0 0 120 120">
                            {/* Background circle */}
                            <circle cx="60" cy="60" r="55" fill="#1A1A1A" stroke="#9F0000" strokeWidth="1" />

                            {/* Progress arc */}
                            <circle
                              cx="60"
                              cy="60"
                              r="55"
                              fill="none"
                              stroke="#FF0044"
                              strokeWidth="3"
                              strokeDasharray={2 * Math.PI * 55}
                              strokeDashoffset={2 * Math.PI * 55 * (1 - level.filled / level.total)}
                              transform="rotate(-90 60 60)"
                            />

                            {/* Center text */}
                            <text x="60" y="55" textAnchor="middle" fill="white" fontSize="24" fontWeight="bold">
                              {level.filled}
                            </text>
                            <text x="60" y="75" textAnchor="middle" fill="white" fontSize="12">
                              of {level.total}
                            </text>
                          </svg>

                          {/* Status indicator */}
                          <div className={`absolute bottom-2 right-2 w-4 h-4 rounded-full ${
                            level.status === 'complete' ? 'bg-green-500' :
                            level.status === 'active' ? 'bg-primary' : 'bg-white/30'
                          }`}></div>
                        </div>
                        <div className="mt-2 text-highlight text-sm font-medium">${level.earnings.toFixed(2)}</div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Partners List */}
                <div className="bg-background rounded-lg border border-primary/20">
                  <div className="p-3 border-b border-primary/20 flex justify-between items-center">
                    <h3 className="text-sm font-medium text-white">Matrix Partners</h3>
                    <div className="text-white/70 text-xs">{currentMatrix.partners.length} members</div>
                  </div>

                  <div className="max-h-80 overflow-y-auto">
                    {currentMatrix.partners.map((partner) => (
                      <motion.div
                        key={partner.id}
                        className="p-3 border-b border-primary/10 hover:bg-secondary/10"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <div className="text-white text-sm font-medium">{partner.name}</div>
                            <div className="text-white/50 text-xs">ID: #{partner.id} • Joined: {formatDate(partner.date)}</div>
                          </div>
                          <div className="flex items-center">
                            <div className="bg-primary/10 text-primary text-xs px-2 py-1 rounded mr-2">
                              Level {partner.level}
                            </div>
                            <div className="bg-background text-white/70 text-xs px-2 py-1 rounded border border-primary/20">
                              Pos {partner.position}
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Sidebar Column */}
            <div className="md:col-span-4 space-y-6">
              {/* Matrix Stats */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <h2 className="text-sm text-white/70 mb-3 flex items-center">
                  <span className="w-1 h-4 bg-primary mr-2"></span>
                  Matrix Stats
                </h2>

                <div className="space-y-3">
                  <div className="bg-background rounded-lg p-3 border border-primary/20">
                    <div className="text-white/70 text-xs mb-1">Total Matrices</div>
                    <div className="text-white text-xl font-bold">{Object.keys(matrixData).length}</div>
                    <div className="text-white/50 text-[10px] mt-1">Active matrix systems</div>
                  </div>

                  <div className="bg-background rounded-lg p-3 border border-primary/20">
                    <div className="text-white/70 text-xs mb-1">Total Partners</div>
                    <div className="text-white text-xl font-bold">
                      {Object.values(matrixData).reduce((sum, matrix) => sum + matrix.partners.length, 0)}
                    </div>
                    <div className="text-white/50 text-[10px] mt-1">Across all matrices</div>
                  </div>

                  <div className="bg-background rounded-lg p-3 border border-primary/20">
                    <div className="text-white/70 text-xs mb-1">Total Earnings</div>
                    <div className="text-white text-xl font-bold">
                      ${Object.values(matrixData).reduce((sum, matrix) => sum + matrix.totalEarnings, 0).toFixed(2)}
                    </div>
                    <div className="text-white/50 text-[10px] mt-1">From all matrix levels</div>
                  </div>
                </div>
              </motion.div>

              {/* Matrix Info */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <h2 className="text-sm text-white/70 mb-3 flex items-center">
                  <span className="w-1 h-4 bg-primary mr-2"></span>
                  How It Works
                </h2>

                <div className="bg-background rounded-lg p-3 border border-primary/20 mb-3">
                  <h3 className="text-white text-sm font-medium mb-2">Matrix System</h3>
                  <p className="text-white/70 text-xs">
                    The Eternal Matrix System uses a recursive spillover structure where each matrix has multiple levels.
                    When your matrix fills up, you earn commissions and spillover to your upline.
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-start">
                    <div className="bg-primary/20 rounded-full p-1 mr-2 flex-shrink-0 mt-0.5">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-primary" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="text-white/70 text-xs">Each matrix has a different structure and payout system</div>
                  </div>

                  <div className="flex items-start">
                    <div className="bg-primary/20 rounded-full p-1 mr-2 flex-shrink-0 mt-0.5">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-primary" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="text-white/70 text-xs">When your matrix fills, you automatically move to the next level</div>
                  </div>

                  <div className="flex items-start">
                    <div className="bg-primary/20 rounded-full p-1 mr-2 flex-shrink-0 mt-0.5">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-primary" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="text-white/70 text-xs">Earn from both direct referrals and spillovers from your upline</div>
                  </div>

                  <div className="flex items-start">
                    <div className="bg-primary/20 rounded-full p-1 mr-2 flex-shrink-0 mt-0.5">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-primary" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="text-white/70 text-xs">All transactions are recorded on the BSC blockchain for transparency</div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Matrix;
