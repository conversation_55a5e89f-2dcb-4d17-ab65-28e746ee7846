import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';

// Mock Data
import mockUserData from '../data/mockUserData';

const Profile = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [userData, setUserData] = useState({
    ...mockUserData,
    profileImage: mockUserData.profileImage || null,
    name: mockUserData.name || 'Eternal User',
    email: '<EMAIL>',
    telegram: '@eternaluser',
    country: 'United States',
    language: 'English',
    notifications: {
      email: true,
      telegram: false,
      earnings: true,
      team: true,
      system: false
    }
  });

  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({ ...userData });
  const fileInputRef = useRef(null);

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setFormData({
          ...formData,
          profileImage: reader.result
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      setFormData({
        ...formData,
        notifications: {
          ...formData.notifications,
          [name]: checked
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleSave = () => {
    setUserData(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(userData);
    setIsEditing(false);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  return (
    <div className="flex flex-col min-h-screen bg-background text-white">
      {/* Background effects */}
      <div className="fixed inset-0 z-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-black to-background opacity-90"></div>
        {/* Animated glow effects */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full bg-primary/5 filter blur-[100px] animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full bg-primary/5 filter blur-[100px] animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>


      {/* Main Content */}
      <div className="flex-1 flex flex-col relative z-10 w-full">
        <div className="flex-1 p-4 md:p-6 overflow-x-hidden max-w-7xl mx-auto w-full">
          <div className="md:grid md:grid-cols-12 md:gap-6 space-y-6 md:space-y-0">
            {/* Sidebar Column */}
            <div className="md:col-span-4 space-y-6">
              {/* Profile Card */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="flex flex-col items-center">
                  <div className="relative group mb-4">
                    <div className="w-24 h-24 rounded-full bg-background flex items-center justify-center overflow-hidden border-2 border-primary/50 shadow-lg">
                      {userData.profileImage ? (
                        <img
                          src={userData.profileImage}
                          alt="Profile"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <span className="text-primary font-bold text-4xl">
                          {userData.name.charAt(0)}
                        </span>
                      )}

                      {isEditing && (
                        <div
                          className="absolute inset-0 bg-black/60 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                          onClick={() => fileInputRef.current.click()}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                        </div>
                      )}
                    </div>

                    {isEditing && (
                      <input
                        type="file"
                        ref={fileInputRef}
                        className="hidden"
                        accept="image/*"
                        onChange={handleImageUpload}
                      />
                    )}

                    {/* Level badge */}
                    <div className="absolute -bottom-1 -right-1 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-xs font-bold border-2 border-background shadow-lg">
                      {userData.level}
                    </div>
                  </div>

                  <h2 className="text-white text-xl font-bold mb-1">{userData.name}</h2>
                  <div className="text-white/50 text-sm mb-3">ID: {userData.id}</div>

                  <div className="bg-background rounded-lg p-3 border border-primary/20 w-full mb-3">
                    <div className="flex justify-between items-center">
                      <div className="text-white/70 text-xs">Wallet Address</div>
                      <div className="text-white/50 text-xs">BSC</div>
                    </div>
                    <div className="text-white text-sm font-medium mt-1">{userData.address}</div>
                  </div>

                  <div className="grid grid-cols-2 gap-3 w-full">
                    <div className="bg-background rounded-lg p-3 border border-primary/20">
                      <div className="text-white/70 text-xs mb-1">Join Date</div>
                      <div className="text-white text-sm font-medium">{formatDate(userData.joinDate)}</div>
                    </div>

                    <div className="bg-background rounded-lg p-3 border border-primary/20">
                      <div className="text-white/70 text-xs mb-1">Status</div>
                      <div className="flex items-center">
                        <div className="h-2 w-2 rounded-full bg-green-500 mr-1"></div>
                        <span className="text-white text-sm font-medium">Active</span>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Navigation Tabs */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <div className="space-y-2">
                  <button
                    className={`w-full text-left px-3 py-2 rounded-lg text-sm ${
                      activeTab === 'profile'
                        ? 'bg-primary text-white'
                        : 'bg-background text-white/70 hover:bg-background/80'
                    }`}
                    onClick={() => setActiveTab('profile')}
                  >
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      Profile Settings
                    </div>
                  </button>

                  <button
                    className={`w-full text-left px-3 py-2 rounded-lg text-sm ${
                      activeTab === 'security'
                        ? 'bg-primary text-white'
                        : 'bg-background text-white/70 hover:bg-background/80'
                    }`}
                    onClick={() => setActiveTab('security')}
                  >
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                      Security
                    </div>
                  </button>

                  <button
                    className={`w-full text-left px-3 py-2 rounded-lg text-sm ${
                      activeTab === 'notifications'
                        ? 'bg-primary text-white'
                        : 'bg-background text-white/70 hover:bg-background/80'
                    }`}
                    onClick={() => setActiveTab('notifications')}
                  >
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                      </svg>
                      Notifications
                    </div>
                  </button>
                </div>
              </motion.div>
            </div>

            {/* Main Content Column */}
            <div className="md:col-span-8 space-y-6">
              {/* Profile Settings */}
              {activeTab === 'profile' && (
                <motion.div
                  className="bg-secondary rounded-lg p-4 border border-primary/20"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-lg font-bold text-white">Profile Settings</h2>

                    {isEditing ? (
                      <div className="flex space-x-2">
                        <button
                          className="bg-background text-white/70 px-3 py-1 rounded-lg text-sm hover:bg-background/80"
                          onClick={handleCancel}
                        >
                          Cancel
                        </button>
                        <button
                          className="bg-primary text-white px-3 py-1 rounded-lg text-sm hover:bg-primary/90"
                          onClick={handleSave}
                        >
                          Save Changes
                        </button>
                      </div>
                    ) : (
                      <button
                        className="bg-primary text-white px-3 py-1 rounded-lg text-sm hover:bg-primary/90"
                        onClick={() => setIsEditing(true)}
                      >
                        Edit Profile
                      </button>
                    )}
                  </div>

                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-white/70 text-xs mb-1">Display Name</label>
                        {isEditing ? (
                          <input
                            type="text"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            className="w-full bg-background text-white border border-primary/20 rounded px-3 py-2 text-sm focus:outline-none focus:border-primary"
                          />
                        ) : (
                          <div className="bg-background text-white border border-primary/20 rounded px-3 py-2 text-sm">
                            {userData.name}
                          </div>
                        )}
                      </div>

                      <div>
                        <label className="block text-white/70 text-xs mb-1">Email Address</label>
                        {isEditing ? (
                          <input
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            className="w-full bg-background text-white border border-primary/20 rounded px-3 py-2 text-sm focus:outline-none focus:border-primary"
                          />
                        ) : (
                          <div className="bg-background text-white border border-primary/20 rounded px-3 py-2 text-sm">
                            {userData.email}
                          </div>
                        )}
                      </div>

                      <div>
                        <label className="block text-white/70 text-xs mb-1">Telegram Username</label>
                        {isEditing ? (
                          <input
                            type="text"
                            name="telegram"
                            value={formData.telegram}
                            onChange={handleInputChange}
                            className="w-full bg-background text-white border border-primary/20 rounded px-3 py-2 text-sm focus:outline-none focus:border-primary"
                          />
                        ) : (
                          <div className="bg-background text-white border border-primary/20 rounded px-3 py-2 text-sm">
                            {userData.telegram}
                          </div>
                        )}
                      </div>

                      <div>
                        <label className="block text-white/70 text-xs mb-1">Country</label>
                        {isEditing ? (
                          <select
                            name="country"
                            value={formData.country}
                            onChange={handleInputChange}
                            className="w-full bg-background text-white border border-primary/20 rounded px-3 py-2 text-sm focus:outline-none focus:border-primary"
                          >
                            <option value="United States">United States</option>
                            <option value="United Kingdom">United Kingdom</option>
                            <option value="Canada">Canada</option>
                            <option value="Australia">Australia</option>
                            <option value="Germany">Germany</option>
                            <option value="France">France</option>
                            <option value="Japan">Japan</option>
                            <option value="China">China</option>
                            <option value="India">India</option>
                            <option value="Brazil">Brazil</option>
                          </select>
                        ) : (
                          <div className="bg-background text-white border border-primary/20 rounded px-3 py-2 text-sm">
                            {userData.country}
                          </div>
                        )}
                      </div>

                      <div>
                        <label className="block text-white/70 text-xs mb-1">Language</label>
                        {isEditing ? (
                          <select
                            name="language"
                            value={formData.language}
                            onChange={handleInputChange}
                            className="w-full bg-background text-white border border-primary/20 rounded px-3 py-2 text-sm focus:outline-none focus:border-primary"
                          >
                            <option value="English">English</option>
                            <option value="Spanish">Spanish</option>
                            <option value="French">French</option>
                            <option value="German">German</option>
                            <option value="Chinese">Chinese</option>
                            <option value="Japanese">Japanese</option>
                            <option value="Russian">Russian</option>
                            <option value="Arabic">Arabic</option>
                          </select>
                        ) : (
                          <div className="bg-background text-white border border-primary/20 rounded px-3 py-2 text-sm">
                            {userData.language}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Security Settings */}
              {activeTab === 'security' && (
                <motion.div
                  className="bg-secondary rounded-lg p-4 border border-primary/20"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <h2 className="text-lg font-bold text-white mb-4">Security Settings</h2>

                  <div className="space-y-4">
                    <div className="bg-background rounded-lg p-4 border border-primary/20">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="text-white text-sm font-medium">Two-Factor Authentication</h3>
                          <p className="text-white/50 text-xs mt-1">Add an extra layer of security to your account</p>
                        </div>
                        <button className="bg-primary text-white px-3 py-1 rounded-lg text-sm">Enable</button>
                      </div>
                    </div>

                    <div className="bg-background rounded-lg p-4 border border-primary/20">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="text-white text-sm font-medium">Change Password</h3>
                          <p className="text-white/50 text-xs mt-1">Update your account password</p>
                        </div>
                        <button className="bg-background border border-primary/20 text-white/70 px-3 py-1 rounded-lg text-sm">Update</button>
                      </div>
                    </div>

                    <div className="bg-background rounded-lg p-4 border border-primary/20">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="text-white text-sm font-medium">Connected Wallets</h3>
                          <p className="text-white/50 text-xs mt-1">Manage your connected blockchain wallets</p>
                        </div>
                        <button className="bg-background border border-primary/20 text-white/70 px-3 py-1 rounded-lg text-sm">Manage</button>
                      </div>
                    </div>

                    <div className="bg-background rounded-lg p-4 border border-primary/20">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="text-white text-sm font-medium">Login History</h3>
                          <p className="text-white/50 text-xs mt-1">View your recent login activity</p>
                        </div>
                        <button className="bg-background border border-primary/20 text-white/70 px-3 py-1 rounded-lg text-sm">View</button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Notification Settings */}
              {activeTab === 'notifications' && (
                <motion.div
                  className="bg-secondary rounded-lg p-4 border border-primary/20"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-lg font-bold text-white">Notification Settings</h2>

                    {isEditing ? (
                      <div className="flex space-x-2">
                        <button
                          className="bg-background text-white/70 px-3 py-1 rounded-lg text-sm hover:bg-background/80"
                          onClick={handleCancel}
                        >
                          Cancel
                        </button>
                        <button
                          className="bg-primary text-white px-3 py-1 rounded-lg text-sm hover:bg-primary/90"
                          onClick={handleSave}
                        >
                          Save Changes
                        </button>
                      </div>
                    ) : (
                      <button
                        className="bg-primary text-white px-3 py-1 rounded-lg text-sm hover:bg-primary/90"
                        onClick={() => setIsEditing(true)}
                      >
                        Edit Notifications
                      </button>
                    )}
                  </div>

                  <div className="space-y-4">
                    <div className="bg-background rounded-lg p-4 border border-primary/20">
                      <h3 className="text-white text-sm font-medium mb-3">Notification Channels</h3>

                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <div className="text-white/70 text-sm">Email Notifications</div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              name="email"
                              className="sr-only peer"
                              checked={isEditing ? formData.notifications.email : userData.notifications.email}
                              onChange={handleInputChange}
                              disabled={!isEditing}
                            />
                            <div className={`w-11 h-6 bg-background peer-focus:outline-none rounded-full peer ${
                              (isEditing ? formData.notifications.email : userData.notifications.email)
                                ? 'after:translate-x-full after:border-white peer-checked:bg-primary'
                                : 'after:border-white/70'
                            } after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all border border-primary/20`}></div>
                          </label>
                        </div>

                        <div className="flex justify-between items-center">
                          <div className="text-white/70 text-sm">Telegram Notifications</div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              name="telegram"
                              className="sr-only peer"
                              checked={isEditing ? formData.notifications.telegram : userData.notifications.telegram}
                              onChange={handleInputChange}
                              disabled={!isEditing}
                            />
                            <div className={`w-11 h-6 bg-background peer-focus:outline-none rounded-full peer ${
                              (isEditing ? formData.notifications.telegram : userData.notifications.telegram)
                                ? 'after:translate-x-full after:border-white peer-checked:bg-primary'
                                : 'after:border-white/70'
                            } after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all border border-primary/20`}></div>
                          </label>
                        </div>
                      </div>
                    </div>

                    <div className="bg-background rounded-lg p-4 border border-primary/20">
                      <h3 className="text-white text-sm font-medium mb-3">Notification Types</h3>

                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <div className="text-white/70 text-sm">Earnings Updates</div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              name="earnings"
                              className="sr-only peer"
                              checked={isEditing ? formData.notifications.earnings : userData.notifications.earnings}
                              onChange={handleInputChange}
                              disabled={!isEditing}
                            />
                            <div className={`w-11 h-6 bg-background peer-focus:outline-none rounded-full peer ${
                              (isEditing ? formData.notifications.earnings : userData.notifications.earnings)
                                ? 'after:translate-x-full after:border-white peer-checked:bg-primary'
                                : 'after:border-white/70'
                            } after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all border border-primary/20`}></div>
                          </label>
                        </div>

                        <div className="flex justify-between items-center">
                          <div className="text-white/70 text-sm">Team Updates</div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              name="team"
                              className="sr-only peer"
                              checked={isEditing ? formData.notifications.team : userData.notifications.team}
                              onChange={handleInputChange}
                              disabled={!isEditing}
                            />
                            <div className={`w-11 h-6 bg-background peer-focus:outline-none rounded-full peer ${
                              (isEditing ? formData.notifications.team : userData.notifications.team)
                                ? 'after:translate-x-full after:border-white peer-checked:bg-primary'
                                : 'after:border-white/70'
                            } after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all border border-primary/20`}></div>
                          </label>
                        </div>

                        <div className="flex justify-between items-center">
                          <div className="text-white/70 text-sm">System Announcements</div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              name="system"
                              className="sr-only peer"
                              checked={isEditing ? formData.notifications.system : userData.notifications.system}
                              onChange={handleInputChange}
                              disabled={!isEditing}
                            />
                            <div className={`w-11 h-6 bg-background peer-focus:outline-none rounded-full peer ${
                              (isEditing ? formData.notifications.system : userData.notifications.system)
                                ? 'after:translate-x-full after:border-white peer-checked:bg-primary'
                                : 'after:border-white/70'
                            } after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all border border-primary/20`}></div>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
