import React, { useState } from 'react';
import { motion } from 'framer-motion';

// Mock Data
import mockUserData from '../data/mockUserData';

const Referrals = () => {
  const [activeTab, setActiveTab] = useState('direct');
  const [searchTerm, setSearchTerm] = useState('');
  const [copied, setCopied] = useState(false);

  // Sample referral data
  const referralData = {
    direct: [
      { id: 1, name: '<PERSON>', level: 2, date: '2023-08-01', earnings: 25.50, active: true },
      { id: 2, name: '<PERSON>', level: 1, date: '2023-07-25', earnings: 18.75, active: true },
      { id: 3, name: '<PERSON>', level: 3, date: '2023-07-20', earnings: 32.25, active: true },
      { id: 4, name: '<PERSON>', level: 1, date: '2023-07-15', earnings: 15.00, active: false },
      { id: 5, name: '<PERSON>', level: 2, date: '2023-07-10', earnings: 22.50, active: true },
      { id: 6, name: '<PERSON>', level: 1, date: '2023-07-05', earnings: 12.75, active: true },
      { id: 7, name: '<PERSON>', level: 2, date: '2023-07-01', earnings: 20.25, active: false },
      { id: 8, name: '<PERSON> <PERSON>', level: 1, date: '2023-06-25', earnings: 10.50, active: true },
    ],
    team: [
      { id: 9, name: '<PERSON> White', level: 4, date: '2023-08-05', earnings: 45.00, active: true, upline: '<PERSON> <PERSON>' },
      { id: 10, name: 'Lisa Harris', level: 3, date: '2023-08-03', earnings: 35.25, active: true, upline: 'Sarah Johnson' },
      { id: 11, name: 'Daniel Clark', level: 2, date: '2023-07-28', earnings: 22.50, active: true, upline: 'Michael Brown' },
      { id: 12, name: 'Michelle Lewis', level: 3, date: '2023-07-22', earnings: 30.75, active: false, upline: 'John Smith' },
      { id: 13, name: 'Christopher Lee', level: 1, date: '2023-07-18', earnings: 15.00, active: true, upline: 'Emily Davis' },
      { id: 14, name: 'Amanda Walker', level: 2, date: '2023-07-12', earnings: 25.50, active: true, upline: 'David Wilson' },
      { id: 15, name: 'Matthew Hall', level: 1, date: '2023-07-08', earnings: 12.75, active: false, upline: 'Jessica Taylor' },
      { id: 16, name: 'Olivia Young', level: 2, date: '2023-07-03', earnings: 20.25, active: true, upline: 'Robert Martinez' },
    ]
  };

  // Filter data based on search term
  const getFilteredData = () => {
    const data = activeTab === 'direct' ? referralData.direct : referralData.team;

    if (!searchTerm) {
      return data;
    }

    return data.filter(item =>
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.id.toString().includes(searchTerm)
    );
  };

  const filteredData = getFilteredData();

  // Handle copy referral link
  const handleCopyLink = () => {
    navigator.clipboard.writeText(mockUserData.referralLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  return (
    <div className="flex flex-col min-h-screen bg-background text-white">
      {/* Background effects */}
      <div className="fixed inset-0 z-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-black to-background opacity-90"></div>
        {/* Animated glow effects */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full bg-primary/5 filter blur-[100px] animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full bg-primary/5 filter blur-[100px] animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>


      {/* Main Content */}
      <div className="flex-1 flex flex-col relative z-10 w-full">
        <div className="flex-1 p-4 md:p-6 overflow-x-hidden max-w-7xl mx-auto w-full">
          <div className="md:grid md:grid-cols-12 md:gap-6 space-y-6 md:space-y-0">
            {/* Main Content Column */}
            <div className="md:col-span-8 space-y-6">
              {/* Page Header */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <h1 className="text-2xl font-bold text-white mb-2">Referrals</h1>
                <p className="text-white/70">Manage your team and track referral performance</p>
              </motion.div>

              {/* Referral List */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-sm text-white/70 flex items-center">
                    <span className="w-1 h-4 bg-primary mr-2"></span>
                    Your Network
                  </h2>

                  <div className="flex space-x-2">
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="Search..."
                        className="bg-background text-white text-xs border border-primary/20 rounded px-3 py-1 pl-8 w-40"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white/50 absolute left-2 top-1/2 transform -translate-y-1/2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>

                    <div className="flex border border-primary/20 rounded overflow-hidden">
                      <button
                        className={`px-3 py-1 text-xs ${activeTab === 'direct' ? 'bg-primary text-white' : 'bg-background text-white/70'}`}
                        onClick={() => setActiveTab('direct')}
                      >
                        Direct
                      </button>
                      <button
                        className={`px-3 py-1 text-xs ${activeTab === 'team' ? 'bg-primary text-white' : 'bg-background text-white/70'}`}
                        onClick={() => setActiveTab('team')}
                      >
                        Team
                      </button>
                    </div>
                  </div>
                </div>

                {/* Table Header */}
                <div className="bg-background rounded-t-lg border border-primary/20 p-3 grid grid-cols-12 gap-2 text-xs font-medium text-white/70">
                  <div className="col-span-1">#</div>
                  <div className="col-span-3">Name</div>
                  <div className="col-span-2">Level</div>
                  <div className="col-span-2">Joined</div>
                  <div className="col-span-2">Earnings</div>
                  <div className="col-span-2">Status</div>
                </div>

                {/* Table Body */}
                <div className="border-l border-r border-primary/20 bg-background/50">
                  {filteredData.length > 0 ? (
                    filteredData.map((item, index) => (
                      <motion.div
                        key={item.id}
                        className="grid grid-cols-12 gap-2 p-3 text-xs border-b border-primary/20 hover:bg-background/80"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.05 }}
                      >
                        <div className="col-span-1 text-white/70">#{item.id}</div>
                        <div className="col-span-3 text-white font-medium">{item.name}</div>
                        <div className="col-span-2">
                          <span className="bg-primary/20 text-primary px-2 py-0.5 rounded-full">
                            Level {item.level}
                          </span>
                        </div>
                        <div className="col-span-2 text-white/70">{formatDate(item.date)}</div>
                        <div className="col-span-2 text-highlight">${item.earnings.toFixed(2)}</div>
                        <div className="col-span-2">
                          {item.active ? (
                            <span className="bg-green-900/20 text-green-500 px-2 py-0.5 rounded-full flex items-center w-fit">
                              <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1"></span>
                              Active
                            </span>
                          ) : (
                            <span className="bg-red-900/20 text-red-500 px-2 py-0.5 rounded-full flex items-center w-fit">
                              <span className="w-1.5 h-1.5 bg-red-500 rounded-full mr-1"></span>
                              Inactive
                            </span>
                          )}
                        </div>
                      </motion.div>
                    ))
                  ) : (
                    <div className="p-6 text-center text-white/50">
                      No referrals found matching your search.
                    </div>
                  )}
                </div>

                {/* Table Footer */}
                <div className="bg-background rounded-b-lg border-b border-l border-r border-primary/20 p-3 flex justify-between items-center text-xs">
                  <div className="text-white/70">
                    Showing {filteredData.length} of {activeTab === 'direct' ? referralData.direct.length : referralData.team.length} referrals
                  </div>
                  <div className="flex space-x-1">
                    <button className="bg-background border border-primary/20 text-white/70 px-2 py-1 rounded">
                      &lt;
                    </button>
                    <button className="bg-primary border border-primary text-white px-2 py-1 rounded">
                      1
                    </button>
                    <button className="bg-background border border-primary/20 text-white/70 px-2 py-1 rounded">
                      &gt;
                    </button>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Sidebar Column */}
            <div className="md:col-span-4 space-y-6">
              {/* Referral Link */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <h2 className="text-sm text-white/70 mb-3 flex items-center">
                  <span className="w-1 h-4 bg-primary mr-2"></span>
                  Your Referral Link
                </h2>

                <div className="bg-background rounded-lg p-3 border border-primary/20 mb-3 relative">
                  <input
                    type="text"
                    value={mockUserData.referralLink}
                    readOnly
                    className="w-full bg-transparent text-primary text-sm border-none focus:outline-none pr-8"
                  />
                  <button
                    onClick={handleCopyLink}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-primary transition-colors"
                  >
                    {copied ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-highlight" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z" />
                        <path d="M3 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L10.414 13H15v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5zM15 11h2a1 1 0 110 2h-2v-2z" />
                      </svg>
                    )}
                  </button>
                </div>

                <div className="flex justify-center space-x-3 mb-3">
                  <a href="#" className="bg-[#1877F2] text-white p-2 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                    </svg>
                  </a>
                  <a href="#" className="bg-[#1DA1F2] text-white p-2 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                    </svg>
                  </a>
                  <a href="#" className="bg-[#25D366] text-white p-2 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.297-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
                    </svg>
                  </a>
                </div>

                <div className="text-center text-white/50 text-xs">
                  Share this link to earn referral bonuses
                </div>
              </motion.div>

              {/* Referral Stats */}
              <motion.div
                className="bg-secondary rounded-lg p-4 border border-primary/20"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <h2 className="text-sm text-white/70 mb-3 flex items-center">
                  <span className="w-1 h-4 bg-primary mr-2"></span>
                  Referral Stats
                </h2>

                <div className="space-y-3">
                  <div className="bg-background rounded-lg p-3 border border-primary/20 flex justify-between items-center">
                    <div>
                      <div className="text-white/70 text-xs">Direct Referrals</div>
                      <div className="text-white text-xl font-bold">{referralData.direct.length}</div>
                    </div>
                    <div className="bg-primary/20 rounded-full p-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                      </svg>
                    </div>
                  </div>

                  <div className="bg-background rounded-lg p-3 border border-primary/20 flex justify-between items-center">
                    <div>
                      <div className="text-white/70 text-xs">Team Size</div>
                      <div className="text-white text-xl font-bold">{referralData.direct.length + referralData.team.length}</div>
                    </div>
                    <div className="bg-primary/20 rounded-full p-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                  </div>

                  <div className="bg-background rounded-lg p-3 border border-primary/20 flex justify-between items-center">
                    <div>
                      <div className="text-white/70 text-xs">Active Members</div>
                      <div className="text-white text-xl font-bold">
                        {referralData.direct.filter(item => item.active).length + referralData.team.filter(item => item.active).length}
                      </div>
                    </div>
                    <div className="bg-primary/20 rounded-full p-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                  </div>

                  <div className="bg-background rounded-lg p-3 border border-primary/20 flex justify-between items-center">
                    <div>
                      <div className="text-white/70 text-xs">Total Earnings</div>
                      <div className="text-white text-xl font-bold">
                        ${referralData.direct.reduce((sum, item) => sum + item.earnings, 0).toFixed(2)}
                      </div>
                    </div>
                    <div className="bg-primary/20 rounded-full p-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Referrals;
