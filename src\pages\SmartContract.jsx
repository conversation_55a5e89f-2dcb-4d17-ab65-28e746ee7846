import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'

const SmartContract = () => {
  const [contractAddress, setContractAddress] = useState('0x1234567890abcdef1234567890abcdef12345678')
  const [contractBalance, setContractBalance] = useState('0')
  const [totalMembers, setTotalMembers] = useState('0')
  const [weeklyFund, setWeeklyFund] = useState('0')
  const [nextDistribution, setNextDistribution] = useState(null)
  const [isVerified, setIsVerified] = useState(true)
  
  // Simulate fetching contract data
  useEffect(() => {
    // In a real implementation, this would fetch data from the blockchain
    const timer = setTimeout(() => {
      setContractBalance('24,568.42')
      setTotalMembers('4,892')
      setWeeklyFund('9,784.00')
      
      // Set next distribution to next Sunday
      const now = new Date()
      const nextSunday = new Date()
      nextSunday.setDate(now.getDate() + (7 - now.getDay()) % 7)
      nextSunday.setHours(0, 0, 0, 0)
      setNextDistribution(nextSunday)
    }, 1000)
    
    return () => clearTimeout(timer)
  }, [])
  
  return (
    <div className="py-16 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-orbitron font-bold text-primary mb-6">Smart Contract</h1>
          <p className="text-xl text-contrast/80 max-w-3xl mx-auto">
            ETERNAL runs on a secure, verified smart contract on the Binance Smart Chain
          </p>
        </motion.div>
        
        {/* Contract Info */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mb-20"
        >
          <div className="bg-secondary/20 backdrop-blur-sm border border-secondary/30 rounded-lg p-8 neon-border">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h2 className="text-2xl font-orbitron font-bold text-accent mb-6">
                  Contract Details
                </h2>
                
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-white mb-2">Contract Address</h3>
                    <div className="flex items-center">
                      <code className="bg-background/50 text-contrast p-2 rounded-md flex-grow overflow-x-auto">
                        {contractAddress}
                      </code>
                      <button 
                        className="ml-2 p-2 bg-primary/20 text-accent rounded-md hover:bg-primary/30 transition-colors"
                        onClick={() => navigator.clipboard.writeText(contractAddress)}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                        </svg>
                      </button>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium text-white mb-2">Network</h3>
                    <div className="flex items-center">
                      <span className="bg-background/50 text-contrast p-2 rounded-md">
                        Binance Smart Chain (BSC)
                      </span>
                      <div className="ml-2 flex items-center text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="ml-1">Active</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium text-white mb-2">Verification Status</h3>
                    <div className="flex items-center">
                      <span className="bg-background/50 text-contrast p-2 rounded-md">
                        {isVerified ? 'Verified on BSCScan' : 'Pending Verification'}
                      </span>
                      {isVerified ? (
                        <div className="ml-2 flex items-center text-green-500">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <span className="ml-1">Verified</span>
                        </div>
                      ) : (
                        <div className="ml-2 flex items-center text-yellow-500">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <span className="ml-1">Pending</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium text-white mb-2">Token</h3>
                    <div className="flex items-center">
                      <span className="bg-background/50 text-contrast p-2 rounded-md">
                        USDT (BEP20)
                      </span>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium text-white mb-2">Gas Fee</h3>
                    <div className="flex items-center">
                      <span className="bg-background/50 text-contrast p-2 rounded-md">
                        Paid in BNB
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <h2 className="text-2xl font-orbitron font-bold text-accent mb-6">
                  Live Contract Stats
                </h2>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div className="bg-background/50 p-4 rounded-lg border border-secondary/30">
                    <h3 className="text-lg font-medium text-white mb-2">Contract Balance</h3>
                    <p className="text-3xl font-orbitron text-accent">${contractBalance}</p>
                    <p className="text-sm text-contrast/70 mt-1">USDT (BEP20)</p>
                  </div>
                  
                  <div className="bg-background/50 p-4 rounded-lg border border-secondary/30">
                    <h3 className="text-lg font-medium text-white mb-2">Total Members</h3>
                    <p className="text-3xl font-orbitron text-accent">{totalMembers}</p>
                    <p className="text-sm text-contrast/70 mt-1">Active participants</p>
                  </div>
                  
                  <div className="bg-background/50 p-4 rounded-lg border border-secondary/30">
                    <h3 className="text-lg font-medium text-white mb-2">Weekly Fund</h3>
                    <p className="text-3xl font-orbitron text-accent">${weeklyFund}</p>
                    <p className="text-sm text-contrast/70 mt-1">To be distributed</p>
                  </div>
                  
                  <div className="bg-background/50 p-4 rounded-lg border border-secondary/30">
                    <h3 className="text-lg font-medium text-white mb-2">Next Distribution</h3>
                    <p className="text-3xl font-orbitron text-accent">
                      {nextDistribution ? nextDistribution.toLocaleDateString() : 'Loading...'}
                    </p>
                    <p className="text-sm text-contrast/70 mt-1">Sunday, 00:00 UTC</p>
                  </div>
                </div>
                
                <div className="mt-8">
                  <a 
                    href={`https://bscscan.com/address/${contractAddress}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-primary/20 text-white font-orbitron font-bold py-2 px-6 rounded-md hover:bg-primary/30 transition-all duration-300 flex items-center justify-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    View on BSCScan
                  </a>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
        
        {/* Contract Security */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mb-20"
        >
          <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-8 text-center">
            Contract Security
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-background border border-secondary/30 rounded-lg p-6">
              <div className="text-primary text-4xl mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-orbitron font-bold text-white mb-2">Verified & Audited</h3>
              <p className="text-contrast/80">
                Our smart contract has been verified on BSCScan and audited by independent security firms to ensure it's secure and functions as intended.
              </p>
            </div>
            
            <div className="bg-background border border-secondary/30 rounded-lg p-6">
              <div className="text-primary text-4xl mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="text-xl font-orbitron font-bold text-white mb-2">Immutable Rules</h3>
              <p className="text-contrast/80">
                Once deployed, the contract's core rules cannot be changed, ensuring that the system operates exactly as promised without any possibility of manipulation.
              </p>
            </div>
            
            <div className="bg-background border border-secondary/30 rounded-lg p-6">
              <div className="text-primary text-4xl mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h3 className="text-xl font-orbitron font-bold text-white mb-2">Transparent Transactions</h3>
              <p className="text-contrast/80">
                All transactions are recorded on the blockchain and can be verified by anyone, providing complete transparency for all members.
              </p>
            </div>
          </div>
        </motion.div>
        
        {/* How to Interact */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mb-20"
        >
          <h2 className="text-2xl md:text-3xl font-orbitron font-bold text-accent mb-8 text-center">
            How to Interact with the Contract
          </h2>
          
          <div className="bg-secondary/20 backdrop-blur-sm border border-secondary/30 rounded-lg p-8 neon-border">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-orbitron font-bold text-white mb-4">Requirements</h3>
                <ul className="space-y-4">
                  <li className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-white font-bold">1</span>
                    </div>
                    <div>
                      <h4 className="text-lg font-medium text-accent">BSC-Compatible Wallet</h4>
                      <p className="text-contrast/80">MetaMask, Trust Wallet, SafePal, or TokenPocket configured for Binance Smart Chain</p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-white font-bold">2</span>
                    </div>
                    <div>
                      <h4 className="text-lg font-medium text-accent">BNB for Gas</h4>
                      <p className="text-contrast/80">A small amount of BNB to cover transaction fees (approximately 0.001 BNB per transaction)</p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-white font-bold">3</span>
                    </div>
                    <div>
                      <h4 className="text-lg font-medium text-accent">USDT (BEP20)</h4>
                      <p className="text-contrast/80">$6 worth of USDT on the Binance Smart Chain (BEP20 standard)</p>
                    </div>
                  </li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-xl font-orbitron font-bold text-white mb-4">Steps to Join</h3>
                <ul className="space-y-4">
                  <li className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-white font-bold">1</span>
                    </div>
                    <div>
                      <h4 className="text-lg font-medium text-accent">Connect Your Wallet</h4>
                      <p className="text-contrast/80">Click the "Connect Wallet" button and select your wallet provider</p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-white font-bold">2</span>
                    </div>
                    <div>
                      <h4 className="text-lg font-medium text-accent">Enter Referrer Address</h4>
                      <p className="text-contrast/80">Enter the wallet address of the person who referred you (if any)</p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-white font-bold">3</span>
                    </div>
                    <div>
                      <h4 className="text-lg font-medium text-accent">Approve USDT</h4>
                      <p className="text-contrast/80">Approve the contract to use your USDT (one-time approval)</p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-white font-bold">4</span>
                    </div>
                    <div>
                      <h4 className="text-lg font-medium text-accent">Join ETERNAL</h4>
                      <p className="text-contrast/80">Confirm the transaction to join the ETERNAL Income Matrix System</p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
            
            <div className="mt-8 text-center">
              <Link 
                to="/connect-wallet" 
                className="btn-primary text-lg px-8 py-3"
              >
                Connect Wallet & Join
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default SmartContract
