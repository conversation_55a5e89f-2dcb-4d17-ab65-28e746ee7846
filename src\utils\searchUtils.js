import searchableUsers from '../data/searchableUsers';

// Search users by various criteria
export const searchUsers = (query, filters = {}) => {
  if (!query || query.trim().length < 1) {
    return [];
  }

  const searchTerm = query.toLowerCase().trim();
  
  let results = searchableUsers.filter(user => {
    // Search by ID (exact or partial match)
    const idMatch = user.id.toString().includes(searchTerm);
    
    // Search by username (partial match)
    const usernameMatch = user.username.toLowerCase().includes(searchTerm);
    
    // Search by display name (partial match)
    const displayNameMatch = user.displayName.toLowerCase().includes(searchTerm);
    
    // Search by wallet address (partial match)
    const walletMatch = user.walletAddress.toLowerCase().includes(searchTerm);
    
    return idMatch || usernameMatch || displayNameMatch || walletMatch;
  });

  // Apply filters
  if (filters.level && filters.level !== 'all') {
    results = results.filter(user => user.level === parseInt(filters.level));
  }

  if (filters.status && filters.status !== 'all') {
    results = results.filter(user => user.status === filters.status);
  }

  if (filters.verified !== undefined) {
    results = results.filter(user => user.isVerified === filters.verified);
  }

  if (filters.country && filters.country !== 'all') {
    results = results.filter(user => user.country === filters.country);
  }

  // Sort results by relevance (exact ID match first, then by level, then by earnings)
  results.sort((a, b) => {
    // Exact ID match gets highest priority
    const aExactId = a.id.toString() === searchTerm;
    const bExactId = b.id.toString() === searchTerm;
    
    if (aExactId && !bExactId) return -1;
    if (!aExactId && bExactId) return 1;
    
    // Then by level (higher level first)
    if (a.level !== b.level) return b.level - a.level;
    
    // Then by total earnings (higher earnings first)
    return b.totalEarnings - a.totalEarnings;
  });

  return results.slice(0, 10); // Limit to 10 results
};

// Get user by ID
export const getUserById = (id) => {
  return searchableUsers.find(user => user.id === parseInt(id));
};

// Get recent searches from localStorage
export const getRecentSearches = () => {
  try {
    const recent = localStorage.getItem('eternal_recent_searches');
    return recent ? JSON.parse(recent) : [];
  } catch (error) {
    return [];
  }
};

// Save search to recent searches
export const saveRecentSearch = (query) => {
  if (!query || query.trim().length < 2) return;
  
  try {
    let recent = getRecentSearches();
    
    // Remove if already exists
    recent = recent.filter(item => item.query !== query);
    
    // Add to beginning
    recent.unshift({
      query: query.trim(),
      timestamp: new Date().toISOString()
    });
    
    // Keep only last 5 searches
    recent = recent.slice(0, 5);
    
    localStorage.setItem('eternal_recent_searches', JSON.stringify(recent));
  } catch (error) {
    console.error('Error saving recent search:', error);
  }
};

// Clear recent searches
export const clearRecentSearches = () => {
  try {
    localStorage.removeItem('eternal_recent_searches');
  } catch (error) {
    console.error('Error clearing recent searches:', error);
  }
};

// Get popular searches (mock data for now)
export const getPopularSearches = () => {
  return [
    'CryptoKing',
    'MatrixMaster',
    'DiamondHands',
    'EthereumElite',
    'DeFiDynamo'
  ];
};

// Get all unique countries for filter
export const getCountries = () => {
  const countries = [...new Set(searchableUsers.map(user => user.country))];
  return countries.sort();
};

// Get all unique levels for filter
export const getLevels = () => {
  const levels = [...new Set(searchableUsers.map(user => user.level))];
  return levels.sort((a, b) => a - b);
};

// Format wallet address for display
export const formatWalletAddress = (address, length = 8) => {
  if (!address) return '';
  if (address.length <= length + 6) return address;
  
  const start = address.slice(0, length);
  const end = address.slice(-4);
  return `${start}...${end}`;
};

// Format date for display
export const formatDate = (dateString) => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (error) {
    return dateString;
  }
};

// Format currency
export const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

// Get time ago string
export const getTimeAgo = (dateString) => {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    
    return formatDate(dateString);
  } catch (error) {
    return 'Unknown';
  }
};
