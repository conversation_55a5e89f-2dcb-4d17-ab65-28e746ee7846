/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'primary': '#FF0044',       /* Neon Red/Pink */
        'secondary': '#1A1A1A',     /* Dark Grey */
        'background': '#0D0D0D',    /* Pure Black */
        'accent': '#FF0044',        /* Electric Red Highlights */
        'contrast': '#FFFFFF',      /* Primary Text */
        'highlight': '#00FFAA',     /* Success Green */
        'dark': '#0D0D0D',          /* Background */
        'card': '#1A1A1A',          /* Panel BG */
        'chart-purple': '#9F56FF',  /* Purple for charts */
        'chart-blue': '#56ACFF',    /* Blue for charts */
        'chart-green': '#56FFB2',   /* Green for charts */
      },
      fontFamily: {
        'orbitron': ['Orbitron', 'sans-serif'],
        'rajdhani': ['Rajdhani', 'sans-serif'],
      },
    },
  },
  plugins: [],
}
