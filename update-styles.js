// <PERSON>ript to update all card styles in the Dashboard.jsx file
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the Dashboard.jsx file
const dashboardPath = path.join(__dirname, 'src', 'pages', 'Dashboard.jsx');
let dashboardContent = fs.readFileSync(dashboardPath, 'utf8');

// Replace all shadow styles with card-shadow class
dashboardContent = dashboardContent.replace(/shadow-\[0_0_15px_rgba\(159,0,0,0\.2\)\]/g, 'card-shadow');
dashboardContent = dashboardContent.replace(/shadow-glow/g, 'card-shadow');

// Replace all border styles
dashboardContent = dashboardContent.replace(/border border-\[#9F0000\]\/50/g, 'border border-[#9F0000]/20');
dashboardContent = dashboardContent.replace(/border border-\[#9F0000\]/g, 'border border-[#9F0000]/20');
dashboardContent = dashboardContent.replace(/border border-\[#9F0000\]\/30/g, 'border border-[#9F0000]/20');

// Write the updated content back to the file
fs.writeFileSync(dashboardPath, dashboardContent);

console.log('All card styles in Dashboard.jsx have been updated successfully!');
